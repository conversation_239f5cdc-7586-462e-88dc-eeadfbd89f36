// 列表
export interface getListParams {
  title?: string;
  update_user?: string;
  start_time?: string;
  end_time?: string;
}

// 重发
export interface repeatParams {
  title?: string;
  tpl_type?: string;
  content?: string;
  link_title?: string;
  link_pic?: string;
  link_desc?: string;
  link_url?: string;
  attach_files?: string[];
  cron_time?: string;
  core_user_arr?: string[];
}

// 新增
export interface createParams {
  title?: string;
  tpl_type?: string;
  content?: string;
  link_title?: string;
  link_pic?: string;
  link_desc?: string;
  link_url?: string;
  attach_files?: string[];
  cron_time?: string;
  core_user_arr?: string[];
}

// 批量导入
export interface detailListParams {
  core_user: string;
  user_name: string;
  state: string;
  tpl_id: string;
}
