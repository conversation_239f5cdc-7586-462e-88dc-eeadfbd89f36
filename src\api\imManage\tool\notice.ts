import { defHttp } from '/@/utils/http/axios';
import {
  getToolNoticeParams /* , getAdminUserListParamsResultModel */,
  addToolNoticeParams,
  editToolNoticeParams,
  deleteToolNoticeParams,
} from './model/noticeModel';
import { BasicExportParams } from '../../model/baseModel';
export type { addToolNoticeParams, editToolNoticeParams };

enum Api {
  getToolNotice = '/im/notice/list',
  addToolNotice = '/im/notice/create',
  editToolNotice = '/im/notice/update',
  deleteToolNotice = '/im/notice/delete',
  exportToolNotice = '/im/notice/export',
}

/**
 * @description: 获取工具配置会话推送列表
 */
export function getToolNoticeList(params: getToolNoticeParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolNotice,
    params,
  });
}

/**
 * @description: 获取工具配置会话推送详情
 */
export function getToolNoticeDetail(params: addToolNoticeParams) {
  return defHttp.post({
    url: Api.addToolNotice,
    params,
  });
}
/**
 * @description 修改工具配置会话推送
 */
export function editToolNotice(params: editToolNoticeParams) {
  return defHttp.post({
    url: Api.editToolNotice,
    params,
  });
}
/**
 * @description 修改工具配置会话推送
 */
export function deleteToolNotice(params: deleteToolNoticeParams) {
  return defHttp.post({
    url: Api.deleteToolNotice,
    params,
  });
}
/**
 * @description 添加工具配置会话推送
 */
export function addToolNotice(params: addToolNoticeParams) {
  return defHttp.post({
    url: Api.addToolNotice,
    params,
  });
}

/**
 * 导出
 */
export function exportToolNotice() {
  return defHttp.post({
    url: Api.exportToolNotice,
  });
}

/**
 * 导出全部:异步
 */
export function exportToolNoticeQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportToolNotice,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
