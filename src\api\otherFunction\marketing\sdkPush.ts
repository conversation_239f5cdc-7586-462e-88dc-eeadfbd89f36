import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams, gamesParams } from './model/sdkPushModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, gamesParams };
enum Api {
  list = '/marketing/sdk-sys-msg/list',
  create = '/marketing/sdk-sys-msg/add',
  edit = '/marketing/sdk-sys-msg/save',
  delete = '/marketing/sdk-sys-msg/del',
  games = '/marketing/sdk-sys-msg/fetch-games',
  exportList = '/other/send-message/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 编辑
 */
export function editItem(params: createParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteItem(params: createParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}
/**
 * @description 屏蔽游戏列表
 */
export function gamesItem(params: gamesParams) {
  return defHttp.get({
    url: Api.games,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
