import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  detailParams,
  deleteParams,
  remarkParams,
} from './model/accountAssociatedModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, detailParams, deleteParams, remarkParams };
enum Api {
  list = '/game/appeal-record/list',
  detail = '/game/appeal-record/detail',
  data = '/game/appeal-record/detail',
  pass = '/game/appeal-record/pass',
  unpass = '/game/appeal-record/unpass',
  remark = '/game/appeal-record/remark',
  exportList = '/game/appeal-record/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 详情
 */
export function detailItem(params: detailParams) {
  return defHttp.get({
    url: Api.detail,
    params,
  });
}

/**
 * @description 通过
 */
export function passItem(params: deleteParams) {
  return defHttp.post({
    url: Api.pass,
    params,
  });
}

/**
 *
 * @description 不通过
 */
export function unPassItem(params: deleteParams) {
  return defHttp.post({
    url: Api.unpass,
    params,
  });
}
/**
 *
 * @description 备注
 */
export function remarkItem(params: remarkParams) {
  return defHttp.post({
    url: Api.remark,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
