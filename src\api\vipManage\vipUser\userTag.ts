import { defHttp } from '/@/utils/http/axios';
import { getListParams /* , getListListParamsResultModel */ } from './model/userTag';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getList = '/wxwork/qwb-user-arrange/list',
  export = '/wxwork/qwb-user-arrange/export',
}

export function getList(params: getListParams) {
  // <getListListParamsResultModel>
  return defHttp.get({
    url: Api.getList,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportList() {
  return defHttp.get({
    url: Api.export,
  });
}

/**
 * 导出全部:异步
 */
export function exportQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
