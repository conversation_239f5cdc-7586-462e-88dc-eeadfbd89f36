import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams } from './model/sendMessageModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams };
enum Api {
  list = '/other/sms-task/list',
  del = '/other/sms-task/delete',
  create = '/other/sms-task/create',
  edit = '/other/sms-task/update',
  review = '/other/sms-task/review',
  surplusSMS = '/other/send-message/getLeftMessage',
  exportList = '/other/sms-task/review',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 修改
 */
export function updateItem(params: createParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteItem(params: any) {
  return defHttp.post({
    url: Api.del,
    params,
  });
}

/**
 * @description 审核
 */
export function reviewItem(params: any) {
  return defHttp.post({
    url: Api.review,
    params,
  });
}
/**
 * @description 短信剩余数量
 */
export function surplusSMS() {
  return defHttp.get({
    url: Api.surplusSMS,
  });
}
