import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  createParams,
  editParams,
  deleteParams,
} from './model/qualityCategoryModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, editParams, deleteParams };
enum Api {
  list = '/config/qualitied-category/list',
  create = '/config/qualitied-category/create',
  edit = '/config/qualitied-category/update',
  delete = '/config/qualitied-category/delete',
  exportList = '/config/qualitied-category/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
