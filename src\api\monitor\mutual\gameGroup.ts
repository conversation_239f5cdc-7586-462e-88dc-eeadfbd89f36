import { defHttp } from '/@/utils/http/axios';
import { getListParams, getTotalInfoParams, updateParams } from './model/gameGroup';
export type { getListParams, updateParams, getTotalInfoParams };
enum Api {
  list = '/wxwork/wx-group-statistics/list',
  totalinfo = '/wxwork/wx-group-statistics/totalinfo',
  update = '/wxwork/wx-group-chat/update',
  addData = '/wxwork/wx-group-statistics/member-add-data',
  msgData = '/wxwork/wx-group-statistics/member-msg-data',
}
/**
 * @description: 获取列表统计数据
 */
export function getTotalinfo(params: getTotalInfoParams) {
  return defHttp.get({
    url: Api.totalinfo,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function updateItem(params: updateParams) {
  return defHttp.post({
    url: Api.update,
    params,
  });
}
/**
 * @description: 获取列表统计数据
 */
export function getAddData(params: getTotalInfoParams) {
  return defHttp.get({
    url: Api.addData,
    params,
  });
}
/**
 * @description: 获取列表统计数据
 */
export function getMsgData(params: getTotalInfoParams) {
  return defHttp.get({
    url: Api.msgData,
    params,
  });
}
