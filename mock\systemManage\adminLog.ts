import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../_util';

export default [
  {
    url: '/api/admin_log/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            ADMIN: 'ouyangyisheng',
            ATYPE: '工单列表',
            CTIME: '2021-03-08 15:04:34',
            IP: '**************',
            REMARK: '添加工单: 领取3月礼包',
          },
        ],
      });
    },
  },
] as MockMethod[];
