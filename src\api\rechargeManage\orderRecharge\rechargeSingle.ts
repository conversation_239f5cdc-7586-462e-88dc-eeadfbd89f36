import { defHttp } from '/@/utils/http/axios';
import { getListParams, editParams } from './model/rechargeSingleModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams };
enum Api {
  list = '/pay/fill-up-record/list',
  edit = '/pay/fill-up-record/repay',
  exportList = '/pay/fill-up-record/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
