import { defHttp } from '/@/utils/http/axios';
import {
  getUserSummaryListParams /* , getUserSummaryListParamsResultModel */,
  editUserSummaryParams,
  createUserSummaryParams,
  importUserSummaryParams,
  allocationUserSummaryParams,
} from './model/userSummary';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getUserSummary = '/vip/vip-user-info/list',
  createUserSummary = '/vip/vip-user-info/create',
  editUserSummary = '/vip/vip-user-info/update',
  importUserSummary = '/vip/vip-user-info/import',
  allocationUserSummary = '/vip/vip-user-info/batch-assign',
  exportUserSummary = '/vip/vip-user-info/export',
}

export function getUserSummaryList(params: getUserSummaryListParams) {
  // <getUserSummaryListParamsResultModel>
  return defHttp.get({
    url: Api.getUserSummary,
    params,
  });
}

export function allocationUserSummary(params: allocationUserSummaryParams) {
  // <getUserSummaryListParamsResultModel>
  return defHttp.post({
    url: Api.allocationUserSummary,
    params,
  });
}

export function editUserSummary(params: editUserSummaryParams) {
  return defHttp.post({
    url: Api.editUserSummary,
    params,
  });
}

export function createUserSummary(params: createUserSummaryParams) {
  return defHttp.post({
    url: Api.createUserSummary,
    params,
  });
}
export function importUserSummary(params: importUserSummaryParams) {
  return defHttp.post({
    url: Api.importUserSummary,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportUserSummaryList() {
  return defHttp.get({
    url: Api.exportUserSummary,
  });
}

/**
 * 导出全部:异步
 */
export function exportUserSummaryQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportUserSummary,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
