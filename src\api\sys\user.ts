import { defHttp } from '/@/utils/http/axios';
import { LoginParams, LoginResultModel, ModifyPwdParams } from './model/userModel';
import { ErrorMessageMode } from '/@/utils/http/axios/types';
export type { ModifyPwdParams } from './model/userModel';

enum Api {
  Login = '/system/index/login',
  ModifyPwd = '/system/index/chpwd',

  // 缓存页面数据
  getFields = '/api/common/get-fields', // 获取列
  setFiels = '/api/common/set-fields', // 设置列
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}
/**
 *
 * @description 修改密码
 */
export function modifyPws(params: ModifyPwdParams) {
  return defHttp.post({
    url: Api.ModifyPwd,
    params,
  });
}

/**
 * 获取列配置
 * @param params
 */
export function getFields(params: { name: string }) {
  return defHttp.get({
    url: Api.getFields,
    params,
  });
}

/**
 * 设置列配置
 * @param params
 */
export function setFields(params: { name: string; value: any }) {
  return defHttp.post({
    url: Api.setFiels,
    params,
  });
}
