import { defHttp } from '/@/utils/http/axios';

enum Api {
  getPrivilege = '/member/member-privilege-config/list',
  createPrivilege = '/member/member-privilege-config/create',
  editPrivilege = '/member/member-privilege-config/update',
  getInfo = '/member/member-privilege-config/info',
  getTableList = '/api/common/coupons',
}

export function getPrivilegeList(params, headers) {
  return defHttp.get({
    url: Api.getPrivilege,
    params,
    headers,
  });
}

export function editPrivilege(params, headers) {
  return defHttp.post({
    url: Api.editPrivilege,
    params,
    headers,
  });
}

export function createPrivilege(params, headers) {
  return defHttp.post({
    url: Api.createPrivilege,
    params,
    headers,
  });
}

export function getInfo(headers) {
  return defHttp.get({
    url: Api.getInfo,
    headers,
  });
}
export function getTableList(params) {
  return defHttp.get({
    url: Api.getTableList,
    params,
  });
}
