{
	"tpl_vue3_table_config": {
		"scope": "javascript,typescript",
		"prefix": "tpl_vue3_table_config",
		"body": [
			"/**",
      "* 页面基础设置",
      "*/",
      "import { EditRecordRow, ActionItem, BasicColumn } from '/@/components/Table';",
      "import { FormProps, FormSchema } from '/@/components/Table';",

      "/* options 格式转变 */",
      "const changeOptionsKey = (m) => {",
        "\treturn {",
          "\t\tvalue: m.key + '',",
          "\t\tlabel: m.val,",
        "\t};",
      "};",

      "/* TODO 根据需求修改  表格配置 */",
      "export function getTableSetting(AUTH) {",
        "\treturn {",
          "\t\ttableSetting: {",
            "\t\t\texportAll: AUTH.export,",
          "\t\t},",
          "\t\tactionColumn: {",
            "\t\t\twidth: 160,",
            "\t\t\ttitle: '操作',",
            "\t\t\tdataIndex: 'action',",
            "\t\t\tslots: { customRender: 'action' },",
          "\t\t},",
          "\t\tcanResize: true,",
          "\t\tstriped: true,",
          "\t\tbordered: true,",
          "\t\tshowTableSetting: true,",
          "\t\tuseSearchForm: true,",
          "\t\tshowIndexColumn: false,",
        "\t};",
      "}",

      "/* TODO 根据需求修改 查询配置 */",
      "export function searchFormConfig(",
        "\tconfig = {",
          "\t\tdep: [],",
        "\t}",
      "): Partial<FormProps> {",
        "\tconst colProps = {",
          "\t\tsm: 12,",
          "\t\txl: 6,",
          "\t\txxl: 4,",
        "\t};",
        "\treturn {",
          "\t\tlabelWidth: 10,",
          "\t\tfieldMapToTime: [['time', ['start_time', 'end_time'], 'YYYY-MM-DD']],",
          "\t\tschemas: [",
            "\t\t\t{",
              "\t\t\t\tfield: ``,",
              "\t\t\t\tlabel: ``,",
              "\t\t\t\tcomponent: 'Select',",
              "\t\t\t\tcomponentProps: { placeholder: '状态', options: config.dep.map(changeOptionsKey) },",
              "\t\t\t\tcolProps,",
            "\t\t\t},",
          "\t\t],",
        "\t};",
      "}",

      "/* TODO 根据需求修改 操作按钮 */",
      "interface useSearchArticlesParams {",
        "\thandlerEdit: Function; // 编辑",
        "\thandlerDelete: Function; // 删除",
        "\tAUTH: any; // 权限",
      "}",

      "export function useSearchArticles(params: useSearchArticlesParams) {",
        "\tconst { handlerEdit, handlerDelete, AUTH } = params;",

        "\tfunction handleEdit(record: EditRecordRow, column: BasicColumn) {",
          "\t\thandlerEdit && handlerEdit(record, column);",
        "\t}",

        "\tfunction handleDelete(record: EditRecordRow, column: BasicColumn) {",
          "\t\thandlerDelete && handlerDelete(record, column);",
        "\t}",

        "\tfunction createActions(record: EditRecordRow, column: BasicColumn): ActionItem[] {",
          "\t\treturn [",
            "\t\t\t{",
              "\t\t\t\tlabel: '编辑',",
              "\t\t\t\tdisabled: !AUTH.update,",
              "\t\t\t\tonClick: handleEdit.bind(null, record, column),",
            "\t\t\t},",
            "\t\t\t{",
              "\t\t\t\tlabel: '删除',",
              "\t\t\t\tdisabled: !AUTH.delete,",
              "\t\t\t\tcolor: 'error',",
              "\t\t\t\tpopConfirm: {",
                "\t\t\t\t\ttitle: '是否删除',",
                "\t\t\t\t\tconfirm: handleDelete.bind(null, record, column),",
              "\t\t\t\t},",
            "\t\t\t},",
          "\t\t];",
        "\t}",
        "\treturn {",
          "\t\tcreateActions,",
        "\t};",
      "}",

      "/* TODO 根据需求修改 默认编辑表单数据 */",
      "export function getEmptyForm() {",
        "\treturn {",

        "\t};",
      "}",

      "/* TODO 根据需求修改 抽屉内表单配置 */",
      "export function editFormConfig(",
        "\tconfig = {",
          "\t\tdep: [],",
        "\t}",
      "): Required<FormSchema[]> {",
        "\tconst colProps = {",
          "\t\txxl: 24,",
        "\t};",
        "\treturn [",
          "\t\t{",
            "\t\t\tfield: '',",
            "\t\t\tcomponent: 'Select',",
            "\t\t\tlabel: '代金券类型',",
            "\t\t\tcolProps: colProps,",
            "\t\t\tcomponentProps: { options: config.dep.map(changeOptionsKey) },",
            "\t\t\trules: [{ required: true }],",
          "\t\t},",
        "\t];",
      "}",
		],
		"description": "vue3 vben 列表页配置项"
	}
}
