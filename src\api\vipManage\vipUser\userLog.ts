import { defHttp } from '/@/utils/http/axios';
import { getUserLogListParams /* , getUserLogListParamsResultModel */ } from './model/userLog';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getUserLog = '/vip/vip-user-logs/list',
  exportUserLog = '/vip/vip-user-logs/export',
}

export function getUserLogList(params: getUserLogListParams) {
  // <getUserLogListParamsResultModel>
  return defHttp.get({
    url: Api.getUserLog,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportUserLogList() {
  return defHttp.get({
    url: Api.exportUserLog,
  });
}

/**
 * 导出全部:异步
 */
export function exportUserLogQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportUserLog,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
