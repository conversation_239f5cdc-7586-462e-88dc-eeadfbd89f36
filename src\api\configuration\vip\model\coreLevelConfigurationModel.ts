/**
 * @description: 获取管理员管理接口params
 */
export interface getRatingConditionListParams {
  rank?: string;
}

export interface editRatingConditionParams {
  id: number | string;
  rank: string;
  total_amount_begin: string;
  total_amount_end: string;
}
export interface createRatingConditionParams {
  rank: string;
  total_amount_begin: string;
  total_amount_end: string;
}

export interface deleteRatingConditionParams {
  id: string | number;
}
