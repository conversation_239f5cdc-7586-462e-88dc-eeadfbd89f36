import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams, detailParams } from './model/qualityListModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, detailParams };
enum Api {
  list = '/qc/qualitied-order/list',
  create = '/qc/qualitied-order/create',
  detail = '/qc/qualitied-order/detail',
  orderDetail = '/game/flow-work-order/detail',
  exportList = '/qc/qualitied-order/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 详情
 */
export function getDetail(params: detailParams) {
  return defHttp.get({
    url: Api.detail,
    params,
  });
}
/**
 * @description 工单详情
 */
export function getOrderDetail(params: detailParams) {
  return defHttp.get({
    url: Api.orderDetail,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
