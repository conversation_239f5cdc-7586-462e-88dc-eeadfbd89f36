import { defHttp } from '/@/utils/http/axios';
import { getListParams, editParams } from './model/rechargeApplyModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams };
enum Api {
  list = '/pay/order-recharge-rebate/list',
  create = '/pay/order-recharge-rebate/add-apply',
  exportList = '/pay/payment/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 修改
 */
export function createItem(params: editParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
