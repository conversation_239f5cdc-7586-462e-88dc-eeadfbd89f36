import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/analyze';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  // 会话分析
  getImStaticDayList = '/im/im-static-day/list',
  getImStaticSourceList = '/im/im-static-source/list',
  getImStaticTimeList = '/im/im-static-time/list',
  getImStaticQueueList = '/im/im-static-queue/list',
  getImStaticQuesList = '/im/im-static-ques/list',
  getImStaticGameList = '/im/im-static-game/list',
  getImStaticCommentList = '/im/im-static-comment/list',

  exportImStaticDayList = '/im/im-static-day/export',
  exportImStaticSourceList = '/im/im-static-source/export',
  exportImStaticTimeList = '/im/im-static-time/export',
  exportImStaticQueueList = '/im/im-static-queue/export',
  exportImStaticQuesList = '/im/im-static-ques/export',
  exportImStaticGameList = '/im/im-static-game/export',
  exportImStaticCommentList = '/im/im-static-comment/export',

  // 客服分析
  getImStaticServiceList = '/im/im-static-service/list',
  getImStaticConvList = '/im/im-static-conv/list',
  getImStaticScoreList = '/im/im-static-score/list',
  getImStaticStaticList = '/im/im-service-static/list',

  exportImStaticServiceList = '/im/im-static-service/export',
  exportImStaticConvList = '/im/im-static-conv/export',
  exportImStaticScoreList = '/im/im-static-score/export',
  exportImStaticStaticList = '/im/im-service-static/export',

  // 词库分析
  getIntelligentKeywordList = '/im/intelligent-keyword/list',
  getIntelligentSpeakList = '/im/intelligent-speak/list',
  getIntelligentCustomerList = '/im/intelligent-customer/list',
  exportIntelligentKeywordList = '/im/intelligent-keyword/export',
  exportIntelligentSpeakList = '/im/intelligent-speak/export',
  exportIntelligentCustomerList = '/im/intelligent-customer/export',
}

const state = {
  // 会话分析
  im_static_day: {
    api: Api.getImStaticDayList,
    exportApi: Api.exportImStaticDayList,
  },
  im_static_source: {
    api: Api.getImStaticSourceList,
    exportApi: Api.exportImStaticSourceList,
  },
  im_static_time: {
    api: Api.getImStaticTimeList,
    exportApi: Api.exportImStaticTimeList,
  },
  im_static_queue: {
    api: Api.getImStaticQueueList,
    exportApi: Api.exportImStaticQueueList,
  },
  im_static_ques: {
    api: Api.getImStaticQuesList,
    exportApi: Api.exportImStaticQuesList,
  },
  im_static_game: {
    api: Api.getImStaticGameList,
    exportApi: Api.exportImStaticGameList,
  },
  im_static_comment: {
    api: Api.getImStaticCommentList,
    exportApi: Api.exportImStaticCommentList,
  },

  // 客服分析
  im_static_service: {
    api: Api.getImStaticServiceList,
    exportApi: Api.exportImStaticServiceList,
  },
  im_static_conv: {
    api: Api.getImStaticConvList,
    exportApi: Api.exportImStaticConvList,
  },
  im_static_score: {
    api: Api.getImStaticScoreList,
    exportApi: Api.exportImStaticScoreList,
  },
  im_service_static: {
    api: Api.getImStaticStaticList,
    exportApi: Api.exportImStaticStaticList,
  },

  //词库分析
  intelligent_keyword: {
    api: Api.getIntelligentKeywordList,
    exportApi: Api.exportIntelligentKeywordList,
  },
  intelligent_speak: {
    api: Api.getIntelligentSpeakList,
    exportApi: Api.exportIntelligentSpeakList,
  },
  intelligent_customer: {
    api: Api.getIntelligentCustomerList,
    exportApi: Api.exportIntelligentCustomerList,
  },
};

/**
 * 获取数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function getAnalyzeList(params: getListParams) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });

  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
}

/**
 * 导出数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function exportAnalyzeListQueue(params) {
  const keys = Object.keys(params.query).filter((m) => m !== 'type');
  const queryParams = {};
  keys.forEach((m) => {
    queryParams[m] = params.query[m];
  });
  return defHttp.request({
    url: state[params.query.type].exportApi,
    method: 'POST',
    params: {
      query: queryParams,
      suffix: params.suffix,
    },
    headers: {
      'export-type': 'queue',
    },
  });
}
