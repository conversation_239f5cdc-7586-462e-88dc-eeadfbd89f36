import { defHttp } from '/@/utils/http/axios';
import {
  getRatingConditionListParams /* , getRatingConditionListParamsResultModel */,
  editRatingConditionParams,
  createRatingConditionParams,
  deleteRatingConditionParams,
} from './model/ratingConditionModel';
import { BasicExportParams } from '../../model/baseModel';
export type { editRatingConditionParams, createRatingConditionParams };

enum Api {
  getRatingCondition = '/config/vip-rank-config/list',
  createRatingCondition = '/config/vip-rank-config/create',
  editRatingCondition = '/config/vip-rank-config/update',
  deleteRatingCondition = '/config/vip-rank-config/delete',
  exportRatingCondition = '/config/vip-rank-config/export',
}

/**
 * @description: 获取管理员列表
 */
export function getRatingConditionList(params: getRatingConditionListParams) {
  // <getRatingConditionListParamsResultModel>
  return defHttp.get({
    url: Api.getRatingCondition,
    params,
  });
}

/**
 * @description 修改管理员
 */
export function editRatingCondition(params: editRatingConditionParams) {
  return defHttp.post({
    url: Api.editRatingCondition,
    params,
  });
}

/**
 * @description 新增管理员
 */
export function createRatingCondition(params: createRatingConditionParams) {
  return defHttp.post({
    url: Api.createRatingCondition,
    params,
  });
}
/**
 * @description 删除管理员
 */
export function deleteRatingCondition(params: deleteRatingConditionParams) {
  return defHttp.post({
    url: Api.deleteRatingCondition,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportRatingConditionList() {
  return defHttp.get({
    url: Api.exportRatingCondition,
  });
}

/**
 * 导出全部:异步
 */
export function exportRatingConditionQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportRatingCondition,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
