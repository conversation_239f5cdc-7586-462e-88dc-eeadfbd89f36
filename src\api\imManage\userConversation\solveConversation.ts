import { defHttp } from '/@/utils/http/axios';
import { getConvListParams } from './model/solveConversationModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  // 会话分析
  getImMyConvList = '/im/my-conv/list',
  exportImMyConvList = '/im/my-conv/export',
}

/**
 * 获取我解决的会话列表
 * @param params 请求参数
 * @param type 类型
 */
export function getImMyConvList(params: getConvListParams) {
  return defHttp.get({
    url: Api.getImMyConvList,
    params,
  });
}

/**
 * 导出
 */
export function exportImMyConvList() {
  return defHttp.post({
    url: Api.exportImMyConvList,
  });
}

/**
 * 导出全部:异步
 */
export function exportImMyConvListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportImMyConvList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
