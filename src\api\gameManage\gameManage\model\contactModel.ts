/**
 * @description: 获取管理员管理接口params
 */
export interface getContactListParams {
  order_no_title?: string;
  cp_game_id?: string;
  game_id?: string;
  question_id?: string;
  core_account?: string;
  role_id?: string;
  qq?: string;
  wechat?: number;
  createor?: number;
  start_time?: number;
  end_time?: number;
}

export interface createContactParams {
  title: string;
  game_id: string;
  question_id: string;
  core_account: string;
  qq: string;
  content: string;
  wechat?: string;
  game_server_id?: string;
  role_id?: string;
  role_name?: string;
  channel_id: string;
  score: string;
  desc?: string;
}

export interface deleteContactParams {
  id: string | number;
}

export interface checkParams {
  cp_game_id: number;
  core_account: string;
}
