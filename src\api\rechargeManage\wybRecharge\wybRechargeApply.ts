import { defHttp } from '/@/utils/http/axios';
import { getListParams, editParams, GameRebate } from './model/wybRechargeRecordModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams, GameRebate };
enum Api {
  paylist = '/pay/vw-payment/list',
  wyblist = '/pay/payment-coin-order/list',
  add = '/pay/payment-coin-order/add-apply',
  edit = '/pay/payment/repay',
  gameRebate = '/pay/coin-rebate-config/get-game-rebate-config',
  exportList = '/pay/payment/export',
}

/**
 * @description: 获取列表
 */
export function getPayList(params: editParams) {
  return defHttp.get({
    url: Api.paylist,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getWybList(params: editParams) {
  return defHttp.get({
    url: Api.wyblist,
    params,
  });
}
/**
 * @description 修改
 */
export function addItem(params: editParams) {
  return defHttp.post({
    url: Api.add,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
export function getGameRebate(params: GameRebate) {
  return defHttp.get({
    url: Api.gameRebate,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
