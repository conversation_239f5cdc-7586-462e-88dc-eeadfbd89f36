import { defHttp } from '/@/utils/http/axios';

enum Api {
  taskConfig = '/points/points-task-config/list',
  createTaskConfig = '/points/points-task-config/create',
  editTaskConfig = '/points/points-task-config/update',
  deleteTask = '/points/points-task-config/delete',
}

export function getTaskConfig(params, headers) {
  return defHttp.get({
    url: Api.taskConfig,
    params,
    headers,
  });
}

export function createTaskConfig(params, headers) {
  return defHttp.post({
    url: Api.createTaskConfig,
    params,
    headers,
  });
}

export function editTaskConfig(params, headers) {
  return defHttp.post({
    url: Api.editTaskConfig,
    params,
    headers,
  });
}

export function deleteTask(params, headers) {
  return defHttp.post({
    url: Api.deleteTask,
    params,
    headers,
  });
}
