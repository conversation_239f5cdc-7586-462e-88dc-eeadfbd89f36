import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/angleQueryModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams };
enum Api {
  list = '/account/role-create-log/list',
  failList = '/account/role-create-log/fail-list',
  exportList = '/account/role-create-log/export',
  detailList = '/account/role-create-log/role-pay-list',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
export function getFailList(params: any) {
  return defHttp.get({
    url: Api.failList,
    params,
  });
}
// 详情
export function getDetail(params: any) {
  return defHttp.get({
    url: Api.detailList,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
