import { defHttp } from '/@/utils/http/axios';

enum Api {
  goodList = '/points/points-goods/list',
  createGoods = '/points/points-goods/create',
  editGoods = '/points/points-goods/update',
}

export function getGoodList(params, headers) {
  return defHttp.get({
    url: Api.goodList,
    params,
    headers,
  });
}

export function editGoods(params, headers) {
  return defHttp.post({
    url: Api.editGoods,
    params,
    headers,
  });
}

export function createGoods(params, headers) {
  return defHttp.post({
    url: Api.createGoods,
    params,
    headers,
  });
}
