{
	// Place your 全局 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
	// Placeholders with the same ids are connected.
	// Example:
	"tpl_vue2_BaseDrawerList": {
		"scope": "javascript,typescript,vue",
		"prefix": "tpl_vue2_BaseDrawerList",
		"body": [
			"<template>",
        "\t<div>",
          "\t\t<!-- 列表: $1 -->",
          "\t\t<BaseDrawerList :data=\"stateData\" />",
        "\t</div>",
      "</template>",
      "<script lang=\"ts\">",
        "\timport { defineComponent } from 'vue';",
        "\timport { searchFormConfig, editFormConfig, getEmptyForm } from './config';",
        "//TODO: 根据需求换接口链接",
        "\timport {",
          "\t\tgetList,",
          "\t\texportListQueue,",
          "\t\tdeleteItem,",
          "\t\tcreateItem,",
          "\t\teditItem,",
        "\t} from '/@/api/configuration/quality/qualityItem';",
        "\timport { BaseDrawerList } from '/@/views/components/baseList';",

        "\texport default defineComponent({",
          "\t\tname: '$2',",
          "\t\tcomponents: { BaseDrawerList },",
          "\t\tsetup() {",
            "\t\t\tconst stateData = {",
              "\t\t\t\tapiConfig: {",
                "\t\t\t\t\tlist: getList,",
                "\t\t\t\t\texport: exportListQueue,",
                "\t\t\t\t\tdelete: deleteItem,",
                "\t\t\t\t\tcreate: createItem,",
                "\t\t\t\t\tupdate: editItem,",
              "\t\t\t\t},",
              "\t\t\t\tsearchFormConfig,",
              "\t\t\t\tgetEmptyForm,",
              "\t\t\t\ttitle: '',",
              "\t\t\t\teditFormConfig,",
              "\t\t\t\toptionsParams: {",
                "\t\t\t\t\tschema: '$3',",
                "\t\t\t\t\toptions: '$4',",
              "\t\t\t\t},",
            "\t\t\t};",
            "\t\t\treturn {",
              "\t\t\t\tstateData,",
            "\t\t\t};",
          "\t\t},",
        "\t});",
      "</script>",

		],
		"description": "tpl_vue2_BaseDrawerList"
	}
}
