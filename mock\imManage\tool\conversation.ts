import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../../_util';

export default [
  {
    url: '/api/tool/conversation/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            name: '登录不上',
            pid: '-',
            ord: '0',
            ext: '',
          },
        ],
      });
    },
  },
  {
    url: '/api/tool/conversation/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        name: `登录不上${id}`,
        pid: '-',
        ord: '0',
      });
    },
  },
  {
    url: '/api/tool/conversation/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as <PERSON>ckMethod[];
