import { useCommonApi } from './common';
enum Api {
  // 总览
  getOverviewList = '/qc/online-kefu-stats-overview/list',
  exportOverviewList = '/qc/online-kefu-stats-overview/export',

  // 大类
  getCateList = '/qc/online-kefu-stats-cate/list',
  exportCateList = '/qc/online-kefu-stats-cate/export',

  // 客服
  getItemList = '/qc/online-kefu-stats-item/list',
  exportItemList = '/qc/online-kefu-stats-item/export',
}

const state = {
  // 总览
  overview: {
    api: Api.getOverviewList,
    exportApi: Api.exportOverviewList,
  },
  // 大类
  cate: {
    api: Api.getCateList,
    exportApi: Api.exportCateList,
  },
  // 客服
  item: {
    api: Api.getItemList,
    exportApi: Api.exportItemList,
  },
};

const { getList, exportListQueue } = useCommonApi(state);

export { getList, exportListQueue };
