/**
 * @description: 获取菜单接口params
 */
export interface getMenuListParams {
  pid: string;
  name: string;
}

export interface editMenuParams {
  id: number | string;
  pid: string;
  name: string;
  mkey?: string;
  url?: string;
  ord?: string;
  icon?: string;
}
export interface createMenuParams {
  pid: string;
  name: string;
  mkey?: string;
  url?: string;
  ord?: string;
  icon?: string;
}
export interface deleteMenuParams {
  id: number | string;
}
