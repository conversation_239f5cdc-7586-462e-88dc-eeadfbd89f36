import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../../_util';

export default [
  {
    url: '/api/tool/faqList/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            ADD_TIME: '2019-02-20 11:55:03',
            CONTENT:
              '账号遗忘：帐号一旦创建&nbsp;不会丢失,&nbsp;如果忘记帐号&nbsp;请点击下面的链接,&nbsp;按照步骤尽可能详细的填写内容(手机用户打不开可以尝试不同浏览器)&nbsp;http://',
            ID: '5',
            ORD: '1',
            QID: '账号问题',
            TITLE: '忘记账号/密码',
            UPDATE_TIME: '2020-02-21 14:26:32',
            USEFUL_NUM: '298',
            USELESS_NUM: '2572',
            VIEW_NUM: '35574',
          },
        ],
      });
    },
  },
  {
    url: '/api/tool/faqList/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        ADD_TIME: '2019-02-20 11:55:03',
        CONTENT:
          '账号遗忘：帐号一旦创建&nbsp;不会丢失,&nbsp;如果忘记帐号&nbsp;请点击下面的链接,&nbsp;按照步骤尽可能详细的填写内容(手机用户打不开可以尝试不同浏览器)&nbsp;http://',
        ID: '5',
        ORD: '1',
        QID: `账号问题${id}`,
        TITLE: '忘记账号/密码',
        UPDATE_TIME: '2020-02-21 14:26:32',
        USEFUL_NUM: '298',
        USELESS_NUM: '2572',
        VIEW_NUM: '35574',
      });
    },
  },
  {
    url: '/api/tool/faqList/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
