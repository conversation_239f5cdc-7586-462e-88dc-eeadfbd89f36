import { defHttp } from '/@/utils/http/axios';

enum Api {
  getBlack = '/member/member-black-log/list',
  createBlack = '/member/member-log/create',
  getBlackLog = '/member/member-black-log/info',
}

export function getBlackList(params, headers) {
  return defHttp.get({
    url: Api.getBlack,
    params,
    headers,
  });
}

export function createBlack(params, headers) {
  return defHttp.post({
    url: Api.createBlack,
    params,
    headers,
  });
}

export function getBlackLogList(params, headers) {
  return defHttp.post({
    url: Api.getBlackLog,
    params,
    headers,
  });
}
