// 列表
export interface getListParams {
  order_no: string;
  type: string;
  inspected: string;
  rater: string;
  score_type: string;
  start_time: string;
  end_time: string;
}
interface dataParams {
  qualitied_item_id: string;
  error_type: string;
  error_num: string;
}
// 新增
export interface createParams {
  order_no: string;
  remark: string;
  score: string | number;
  data: dataParams[];
  type: string | number;
}
// 详情
export interface detailParams {
  order_no?: string;
  type?: string | number;
}
