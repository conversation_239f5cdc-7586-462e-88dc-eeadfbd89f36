{
  // Place your 全局 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  "tpl_vue3_api_model": {
    "scope": "javascript,typescript",
    "prefix": "tpl_vue3_api_model",
    "body": [
      "// 列表",
      "export interface getListParams {}",

      "// 新增",
      "export interface createParams {}",

      "// 编辑",
      "export interface editParams {}",

      "// 删除",
      "export interface deleteParams {}"
    ],
    "description": "tpl_vue3_api_model"
  }
}
