/**
 * @description: 获取权限管理接口params
 */
export interface getAdminGroupListParams {
  name?: string;
}

/**
 * @description: 获取权限管理详情params
 */
export interface addAdminGroupParams {
  name: string;
  permissions: permissions[];
}
interface permissions {
  menu_id: string | number;
  selected: string[] | number[];
}
export interface editAdminGroupParams {
  id: number | string;
  name: string;
  permissions: permissions[];
}

export interface getGroupPowerParams {
  group_id?: number | string;
}

export interface addGroupGiveParams {
  menu_id: number | string;
  url?: string;
  component?: string;
  name?: string;
  path?: string;
  visable?: string;
}
export interface editGroupGiveParams {
  id: number | string;
  menu_id: number | string;
  url?: string;
  component?: string;
  name?: string;
  path?: string;
  visable?: string;
}
export interface deleteGroupGiveParams {
  id: number | string;
}
export interface resetGroupGiveParams {
  id: number | string;
}
