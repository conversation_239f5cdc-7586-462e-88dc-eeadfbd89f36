@basic-menu-prefix-cls: ~'@{namespace}-basic-menu';

.app-top-menu-popup {
  min-width: 150px;
}

.@{basic-menu-prefix-cls} {
  width: 100%;

  .ant-menu-item {
    transition: unset;
  }

  &__sidebar-hor {
    &.ant-menu-horizontal {
      display: flex;
      align-items: center;

      &.ant-menu-dark {
        background: transparent;

        .ant-menu-submenu:hover,
        .ant-menu-item-open,
        .ant-menu-submenu-open,
        .ant-menu-item-selected,
        .ant-menu-submenu-selected,
        .ant-menu-item:hover,
        .ant-menu-item-active,
        .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
        .ant-menu-submenu-active,
        .ant-menu-submenu-title:hover {
          color: #fff;
          background: @top-menu-active-bg-color !important;
        }

        .ant-menu-item:hover,
        .ant-menu-item-active,
        .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
        .ant-menu-submenu-active,
        .ant-menu-submenu-title:hover {
          background: @top-menu-active-bg-color;
        }

        .@{basic-menu-prefix-cls}-item__level1 {
          background: transparent;

          &.ant-menu-item-selected,
          &.ant-menu-submenu-selected {
            background: @top-menu-active-bg-color !important;
          }
        }

        .ant-menu-item,
        .ant-menu-submenu {
          &.@{basic-menu-prefix-cls}-item__level1,
          .ant-menu-submenu-title {
            height: @header-height;
            line-height: @header-height;
          }
        }
      }
    }
  }

  .ant-menu-submenu,
  .ant-menu-submenu-inline {
    transition: unset;
  }

  .ant-menu-inline.ant-menu-sub {
    box-shadow: unset !important;
    transition: unset;
  }
}
