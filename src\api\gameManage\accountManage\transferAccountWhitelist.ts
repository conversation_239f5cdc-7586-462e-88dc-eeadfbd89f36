import { defHttp } from '/@/utils/http/axios';
import { getListParams, deleteParams, importParams } from './model/transferAccountWhitelistModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, importParams };
enum Api {
  list = '/account/zd-white-list/list',
  import = '/account/zd-white-list/create-more',
  delete = '/account/zd-white-list/delete',
  exportList = '/account/zd-white-list/export',
  // resetName = '/account/contact-user/transfer',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function importData(params: importParams) {
  return defHttp.post({
    url: Api.import,
    params,
  });
}

/**
 * @description: 实名重置
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
