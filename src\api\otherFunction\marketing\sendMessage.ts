import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams } from './model/sendMessageModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams };
enum Api {
  list = '/other/send-message/list',
  create = '/other/send-message/create',
  surplusSMS = '/other/send-message/getLeftMessage',
  exportList = '/other/send-message/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 短信剩余数量
 */
export function surplusSMS() {
  return defHttp.get({
    url: Api.surplusSMS,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
