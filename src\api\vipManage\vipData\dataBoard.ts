import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/dataBoardModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getDataBoardAllList = '/vip/vip-market-daliy-report/list',
  getDataBoardGameList = '/vip/vip-market-game-report/list',
  getDataBoardLevelList = '/vip/vip-level-distribu/list',

  exportDataBoardAllList = '/vip/vip-market-daliy-report/export',
  exportDataBoardGameList = '/vip/vip-market-game-report/export',
  exportDataBoardLevelList = '/vip/vip-level-distribu/export',
}

const state = {
  vip_market_daliy_report: {
    api: Api.getDataBoardAllList,
    exportApi: Api.exportDataBoardAllList,
  },
  vip_market_game_report: {
    api: Api.getDataBoardGameList,
    exportApi: Api.exportDataBoardGameList,
  },
  vip_level_distribu: {
    api: Api.getDataBoardLevelList,
    exportApi: Api.exportDataBoardLevelList,
  },
};

/**
 * 获取数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function getAnalyzeList(params: getListParams) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });

  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
}

/**
 * 导出数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function exportAnalyzeListQueue(params) {
  /* const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  }); */
  /*
  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
 */
  return defHttp.request({
    url: state[params.type].exportApi,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
