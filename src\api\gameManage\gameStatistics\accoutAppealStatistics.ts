import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/accoutAppealStatisticsModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getAccoutAppealAllList = '/game/appeal-static/list',
  getAccoutAppealScoreList = '/game/appeal-static-score/list',

  exportAccoutAppealAllList = '/game/appeal-static/export',
  exportAccoutAppealScoreList = '/game/appeal-static-score/export',
}

const state = {
  appeal_static: {
    api: Api.getAccoutAppealAllList,
    exportApi: Api.exportAccoutAppealAllList,
  },
  appeal_static_score: {
    api: Api.getAccoutAppealScoreList,
    exportApi: Api.exportAccoutAppealScoreList,
  },
};

/**
 * 获取数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function getList(params: getListParams) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });

  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
}

/**
 * 导出数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function exportListQueue(params) {
  /* const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  }); */
  /*
  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
 */
  return defHttp.request({
    url: state[params.type].exportApi,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
