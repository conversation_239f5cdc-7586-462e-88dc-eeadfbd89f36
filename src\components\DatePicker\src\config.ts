import moment from 'moment';

export const format = 'YYYY-MM-DD';

export const radioOptions = [
  { value: '0', label: '今天' },
  { value: '-1', label: '昨天' },
  { value: '6', label: '最近7天' },
  { value: '30', label: '最近30天' },
];

export const timeState = {
  // 今天
  '0': [moment(moment().format(format), format), moment(moment().format(format), format)],
  // 昨天
  '-1': [
    moment(moment().subtract(1, 'days').format(format), format),
    moment(moment().subtract(1, 'days').format(format), format),
  ],

  // 最近7天
  '6': [
    moment(moment().subtract(6, 'days').format(format), format),
    moment(moment().format(format), format),
  ],
  // 最近30天
  '30': [
    moment(moment().subtract(30, 'days').format(format), format),
    moment(moment().format(format), format),
  ],
};
