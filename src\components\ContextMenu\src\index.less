@default-height: 42px !important;

@small-height: 36px !important;

@large-height: 36px !important;

.item-style() {
  li {
    display: inline-block;
    width: 100%;
    height: @default-height;
    margin: 0 !important;
    line-height: @default-height;

    span {
      line-height: @default-height;
    }

    > div {
      margin: 0 !important;
    }

    &:not(.ant-menu-item-disabled):hover {
      color: @text-color-base;
      background: #eee;
    }
  }
}

.context-menu {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 200;
  display: block;
  width: 156px;
  margin: 0;
  list-style: none;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 0.25rem;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.1),
    0 1px 5px 0 rgba(0, 0, 0, 0.06);
  background-clip: padding-box;
  user-select: none;

  .item-style();

  .ant-divider {
    margin: 0 0;
  }

  &__popup {
    .ant-divider {
      margin: 0 0;
    }

    .item-style();
  }
}
