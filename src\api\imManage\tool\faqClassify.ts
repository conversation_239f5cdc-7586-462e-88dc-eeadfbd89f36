import { defHttp } from '/@/utils/http/axios';
import {
  getToolFaqClassifyParams /* , getAdminUserListParamsResultModel */,
  createToolFaqClassifyParams,
  editToolFaqClassifyParams,
  deleteToolFaqClassifyParams,
} from './model/faqClassifyModel';
import { BasicExportParams } from '../../model/baseModel';
export type { createToolFaqClassifyParams, editToolFaqClassifyParams };

enum Api {
  getToolFaqClassify = '/im/ques-classify/list',
  createToolFaqClassify = '/im/ques-classify/create',
  editToolFaqClassify = '/im/ques-classify/update',
  deleteToolFaqClassify = '/im/ques-classify/delete',
  exportToolFaqClassify = '/im/ques-classify/export',
}

/**
 * @description: 获取问题分类列表
 */
export function getToolFaqClassifyList(params: getToolFaqClassifyParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolFaqClassify,
    params,
  });
}

/**
 * @description 新增问题分类
 */
export function createToolFaqClassify(params: createToolFaqClassifyParams) {
  return defHttp.post({
    url: Api.createToolFaqClassify,
    params,
  });
}
/**
 * @description 修改问题分类
 */
export function editToolFaqClassify(params: editToolFaqClassifyParams) {
  return defHttp.post({
    url: Api.editToolFaqClassify,
    params,
  });
}

/**
 * @description 删除问题分类
 */
export function deleteToolFaqClassify(params: deleteToolFaqClassifyParams) {
  return defHttp.post({
    url: Api.deleteToolFaqClassify,
    params,
  });
}

/**
 * 导出
 */
export function exportToolFaqClassify() {
  return defHttp.post({
    url: Api.exportToolFaqClassify,
  });
}

/**
 * 导出全部:异步
 */
export function exportToolFaqClassifyQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportToolFaqClassify,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
