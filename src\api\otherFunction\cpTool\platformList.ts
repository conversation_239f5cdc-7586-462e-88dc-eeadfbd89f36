import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams, editParams, deleteParams } from './model/platformListModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, editParams, deleteParams };
enum Api {
  list = '/other/platform-tpl/list',
  create = '/other/platform-tpl/create',
  edit = '/other/platform-tpl/update',
  detail = '/other/platform-tpl/detail',
  delete = '/system/menu/delete',
  exportList = '/system/menu/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 详情
 */
export function detailItem(params: any) {
  return defHttp.get({
    url: Api.detail,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
