import { useCommonApi } from './common';

enum Api {
  // 总览
  getOverviewList = '/qc/contact-record-stats-overview/list',
  exportOverviewList = '/qc/contact-record-stats-overview/export',

  // 大类
  getCateList = '/qc/contact-record-stats-cate/list',
  exportCateList = '/qc/contact-record-stats-cate/export',

  // 客服
  getItemList = '/qc/contact-record-stats-item/list',
  exportItemList = '/qc/contact-record-stats-item/export',
}

const state = {
  // 总览
  overview: {
    api: Api.getOverviewList,
    exportApi: Api.exportOverviewList,
  },
  // 大类
  cate: {
    api: Api.getCateList,
    exportApi: Api.exportCateList,
  },
  // 客服
  item: {
    api: Api.getItemList,
    exportApi: Api.exportItemList,
  },
};

const { getList, exportListQueue } = useCommonApi(state);

export { getList, exportListQueue };
