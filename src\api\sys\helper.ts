import { defHttp } from '/@/utils/http/axios';
import { getHelperViewParams, saveHelperViewParams } from './model/helperModel';

enum Api {
  GetHelperView = '/api/helper/view',
  SaveHelperView = '/api/helper/save',
}

/**
 * 获取帮助页
 * @param params
 * @returns
 */
export function getHelperView(params: getHelperViewParams) {
  return defHttp.get(
    {
      url: Api.GetHelperView,
      params,
    },
    {
      isTransformRequestResult: false,
    }
  );
}

/**
 * 保存帮助页
 * @param params
 * @returns
 */
export function saveHelperView(params: saveHelperViewParams) {
  return defHttp.post({
    url: Api.SaveHelperView,
    params,
  });
}
