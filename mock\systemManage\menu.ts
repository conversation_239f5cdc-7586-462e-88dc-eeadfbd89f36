import { Mock<PERSON>ethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../_util';

export default [
  {
    url: '/api/menu/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            NAME: 'admin',
            MKEY: 'admin',
            URL: 'f843fb9ad16c064d6ca9a7226c94a348',
            ICON: 'gg:loadbar-doc',
          },
        ],
      });
    },
  },
  {
    url: '/api/menu/config',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess({
        // 编辑选项列表
        options: {
          PID: [
            {
              title: 'Node1',
              value: '0-0',
              key: '0-0',
              children: [
                {
                  value: '0-0-1',
                  key: '0-0-1',
                  slots: {
                    title: 'title',
                  },
                },
                {
                  title: 'Child Node2',
                  value: '0-0-2',
                  key: '0-0-2',
                },
              ],
            },
            {
              title: 'Node2',
              value: '0-1',
              key: '0-1',
            },
          ],
          MTYPE: [
            {
              label: '选项1',
              value: '1',
            },
          ],
        },

        // 表头数据
        columns: [
          {
            title: 'ID',
            dataIndex: 'ID',
            fixed: 'left',
            width: 100,
            ellipsis: true,
            slots: { customRender: 'ID' },
          },
          {
            title: '菜单名称',
            dataIndex: 'NAME',
            editable: true,
            ellipsis: true,
            slots: { customRender: 'NAME' },
          },
          {
            title: '标识MKEY',
            dataIndex: 'MKEY',
            slots: { customRender: 'MKEY' },
          },
          {
            title: 'URL地址',
            dataIndex: 'URL',
            slots: { customRender: 'URL' },
            ellipsis: true,
            sorter: true,
            defaultHidden: true,
          },
          {
            title: '图标',
            ellipsis: true,
            sorter: true,
            dataIndex: 'ICON',
            slots: { customRender: 'ICON' },
          },
        ],
      });
    },
  },
  {
    url: '/api/menu/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        PID: id,
        NAME: '在线客服',
        MKEY: 'im_manage',
        MTYPE: 'menu',
        URL: '',
        ICON: '',
      });
    },
  },
  {
    url: '/api/menu/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
