import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  editParams,
  checkParams,
  orderListParams,
} from './model/wybRechargeAuditModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams, checkParams, orderListParams };
enum Api {
  list = '/pay/coin-rebate-audit/list',
  paylist = '/pay/vw-payment/select-payment-order',
  wyblist = '/pay/payment-coin-order/select-coin-order',
  edit = '/pay/coin-rebate-audit/save',
  check = '/pay/coin-rebate-config/get-coin-rebate-config',
  exportList = '/pay/payment/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getPayList(params: orderListParams) {
  return defHttp.get({
    url: Api.paylist,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getWybList(params: orderListParams) {
  return defHttp.get({
    url: Api.wyblist,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 万游币充值返利审核-返利比例
 */
export function getCheckItem(params: checkParams) {
  return defHttp.get({
    url: Api.check,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
