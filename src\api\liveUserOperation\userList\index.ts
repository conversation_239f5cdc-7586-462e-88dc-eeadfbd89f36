import { defHttp } from '/@/utils/http/axios';
import { getListParams, updateParams } from './model/indexModel';
export type { getListParams, updateParams };
enum Api {
  list = '/spc/spc-role-info/list',
  update = '/spc/spc-role-info/update',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 编辑
 */
export function updateItem(params: updateParams) {
  return defHttp.post({
    url: Api.update,
    params,
  });
}
