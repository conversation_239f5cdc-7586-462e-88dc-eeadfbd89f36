/**
 * @description: 获取管理员管理接口params
 */
export interface getAdminUserListParams {
  user_name?: string;
  department_id?: string;
  status?: string;
  real_name?: string;
  level?: string;
  group_id?: string;
}

export interface editAdminUserParams {
  id: number | string;
  user_name: string;
  real_name: string;
  password?: string;
  level: string;
  department_id: string;
  group_id: string;
  status: string;
  sex: string;
  email?: string;
}
export interface createAdminUserParams {
  user_name: string;
  real_name: string;
  password?: string;
  level: string;
  department_id: string;
  group_id: string;
  status: string;
  sex: string;
  email?: string;
}

export interface deleteAdminUserParams {
  id: string | number;
}
