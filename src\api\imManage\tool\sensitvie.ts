import { defHttp } from '/@/utils/http/axios';
import {
  getToolSensitiveParams /* , getAdminUserListParamsResultModel */,
  deleteToolSensitiveParams,
  editToolSensitiveParams,
  addToolSensitiveParams,
  importToolSensitiveParams,
} from './model/sensitiveModel';
export type { editToolSensitiveParams, addToolSensitiveParams };

import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getToolSensitive = '/im/words/list',
  importToolSensitive = '/im/words/import',
  deleteToolSensitive = '/im/words/delete',
  editToolSensitive = '/im/words/update',
  addToolSensitive = '/im/words/create',
  exportToolSensitive = '/im/words/export',
}

/**
 * @description: 获取工具配置敏感词列表
 */
export function getToolSensitiveList(params: getToolSensitiveParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolSensitive,
    params,
  });
}

/**
 * @description: 导入工具配置敏感词详情
 */
export function importToolSensitive(params: importToolSensitiveParams) {
  return defHttp.post({
    url: Api.importToolSensitive,
    params,
  });
}
/**
 * @description 修改工具配置敏感词
 */
export function editToolSensitive(params: editToolSensitiveParams) {
  return defHttp.post({
    url: Api.editToolSensitive,
    params,
  });
}
/**
 * @description 删除工具配置敏感词
 */
export function deleteToolSensitive(params: deleteToolSensitiveParams) {
  return defHttp.post({
    url: Api.deleteToolSensitive,
    params,
  });
}
/**
 * @description 删除工具配置敏感词
 */
export function addToolSensitive(params: addToolSensitiveParams) {
  return defHttp.post({
    url: Api.addToolSensitive,
    params,
  });
}

/**
 * @description 导出同步
 */
export function exportToolSensitive() {
  return defHttp.post(
    {
      url: Api.exportToolSensitive,
    },
    {
      isTransformRequestResult: false,
    }
  );
}

/**
 * 导出全部:异步
 */
export function exportToolSensitiveQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportToolSensitive,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
