import { defHttp } from '/@/utils/http/axios';
import {
  getMyContactListParams /* , getMyContactListParamsResultModel */,
  createMyContactParams,
  deleteMyContactParams,
  updateMyContactParams,
} from './model/myContactModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getMyContact = '/game/vip-contact-record/my-list',
  createMyContact = '/game/vip-contact-record/my-create',
  deleteMyContact = '/game/vip-contact-record/my-delete',
  exportMyContact = '/game/vip-contact-record/my-export',
  updateMyContact = '/game/vip-contact-record/my-update',
  getRoleList = '/vip/user-pay-role/list',
}

export function getRoleList(params: getMyContactListParams) {
  // <getMyContactListParamsResultModel>
  return defHttp.get({
    url: Api.getRoleList,
    params,
  });
}

/**
 * @description: 获取管理员列表
 */
export function getMyContactList(params: getMyContactListParams) {
  // <getMyContactListParamsResultModel>
  return defHttp.get({
    url: Api.getMyContact,
    params,
  });
}

export function createMyContact(params: createMyContactParams) {
  return defHttp.post({
    url: Api.createMyContact,
    params,
  });
}

export function updateMyContact(params: updateMyContactParams) {
  return defHttp.post({
    url: Api.updateMyContact,
    params,
  });
}

export function deleteMyContact(params: deleteMyContactParams) {
  return defHttp.post({
    url: Api.deleteMyContact,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportMyContactList() {
  return defHttp.get({
    url: Api.exportMyContact,
  });
}

/**
 * 导出全部:异步
 */
export function exportMyContactQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportMyContact,
    method: 'POST',
    params: {
      ...params,
      self: 1,
    },
    headers: {
      'export-type': 'queue',
    },
  });
}
