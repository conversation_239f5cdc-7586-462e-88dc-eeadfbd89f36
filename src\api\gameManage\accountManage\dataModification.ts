import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  editPasParams,
  editPhoParams,
  editRealParams,
  editEditParams,
} from './model/dataModificationModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editPasParams, editPhoParams, editRealParams, editEditParams };
enum Api {
  list = '/account/user-file/list',
  editPas = '/account/user-file/ch-pwd',
  editPho = '/account/user-file/ch-phone',
  editReal = '/account/user-file/verify-name',
  editMore = '/account/user-file/batchVerifyName',
  exportList = '/account/user-file/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 修改
 */
export function editMoreItem(params: editEditParams) {
  return defHttp.post({
    url: Api.editMore,
    params,
  });
}

/**
 * @description 修改
 */
export function editPasItem(params: editPasParams) {
  return defHttp.post({
    url: Api.editPas,
    params,
  });
}
/**
 * @description 修改
 */
export function editPhoItem(params: editPhoParams) {
  return defHttp.post({
    url: Api.editPho,
    params,
  });
}
/**
 * @description 修改
 */
export function editRealItem(params: editRealParams) {
  return defHttp.post({
    url: Api.editReal,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
