import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/game/bx-member/list',
  export = '/game/bx-member/export',
  create = '/game/bx-member/create',
  update = '/game/bx-member/update',
  check = '/game/bx-member/check',
}
/**
 * @description: 获取列表
 */
export function getList(params?: any) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 创建
 */
export function createItem(params) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description 更新
 */
export function editItem(params) {
  return defHttp.post({
    url: Api.update,
    params,
  });
}

/**
 * @description 检查充值金额
 */
export function checkItem(params) {
  return defHttp.get({
    url: Api.check,
    params,
  });
}

/**
 * 导出全部:异步
 */
export function exportListQueue(params) {
  return defHttp.request({
    url: Api.export,
    method: 'POST',
    params: {
      ...params,
      self: 1,
    },
    headers: {
      'export-type': 'queue',
    },
  });
}
