import { defHttp } from '/@/utils/http/axios';
import {
  getContactListParams /* , getContactListParamsResultModel */,
  createContactParams,
  deleteContactParams,
  checkParams,
} from './model/contactModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getContact = '/game/vip-contact-record/list',
  createContact = '/game/vip-contact-record/create',
  updateContact = '/game/vip-contact-record/update',
  deleteContact = '/game/vip-contact-record/delete',
  exportContact = '/game/vip-contact-record/export',
  getWarning = '/vip/warning-records/list',
  check = '/game/vip-contact-record/check-user-vip-contact',
}

/**
 * @description: 获取列表
 */
export function getContactList(params: getContactListParams) {
  // <getContactListParamsResultModel>
  return defHttp.get({
    url: Api.getContact,
    params,
  });
}

export function getWarningList(params: getContactListParams) {
  return defHttp.get({
    url: Api.getWarning,
    params,
  });
}

/**
 * @description 新增
 */
export function createContact(params: createContactParams) {
  return defHttp.post({
    url: Api.createContact,
    params,
  });
}
/**
 * @description 更新
 */
export function updateContact(params: createContactParams) {
  return defHttp.post({
    url: Api.updateContact,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteContact(params: deleteContactParams) {
  return defHttp.post({
    url: Api.deleteContact,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportContactList() {
  return defHttp.get({
    url: Api.exportContact,
  });
}

/**
 * 导出全部:异步
 */
export function exportContactQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportContact,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}

export function checkVip(params: checkParams) {
  return defHttp.post({
    url: Api.check,
    params,
  });
}
