import { defHttp } from '/@/utils/http/axios';
import {
  getToolConversationParams /* , getAdminUserListParamsResultModel */,
  addToolConversationParams,
  editToolConversationParams,
  deleteToolConversationParams,
} from './model/conversationModel';
import { BasicExportParams } from '../../model/baseModel';
export type { addToolConversationParams, editToolConversationParams };

enum Api {
  getToolConversation = '/im/classify/list',
  addToolConversation = '/im/classify/create',
  editToolConversation = '/im/classify/update',
  deleteToolConversation = '/im/classify/delete',
  exportToolConversation = '/im/classify/export',
}

/**
 * @description: 获取工具配置会话分类列表
 */
export function getToolConversationList(params: getToolConversationParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolConversation,
    params,
  });
}

/**
 * @description: 获取工具配置会话分类详情
 */
export function addToolConversation(params: addToolConversationParams) {
  return defHttp.post({
    url: Api.addToolConversation,
    params,
  });
}
/**
 * @description 修改工具配置会话分类
 */
export function editToolConversation(params: editToolConversationParams) {
  return defHttp.post({
    url: Api.editToolConversation,
    params,
  });
}

/**
 * @description 修改工具配置会话分类
 */
export function deleteToolConversation(params: deleteToolConversationParams) {
  return defHttp.post({
    url: Api.deleteToolConversation,
    params,
  });
}

/**
 * 导出
 */
export function exportToolConversation() {
  return defHttp.post({
    url: Api.exportToolConversation,
  });
}

/**
 * 导出全部:异步
 */
export function exportToolConversationQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportToolConversation,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
