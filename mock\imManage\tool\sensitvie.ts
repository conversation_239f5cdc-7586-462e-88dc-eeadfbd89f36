import { <PERSON>ck<PERSON>ethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../../_util';

export default [
  {
    url: '/api/tool/sensitive/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            word: '我好1',
            add_time: '2020-08-06 18:17:54',
            ext: null,
          },
        ],
      });
    },
  },
  {
    url: '/api/tool/sensitive/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        word: `word${id}`,
      });
    },
  },
  {
    url: '/api/tool/sensitive/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as <PERSON><PERSON><PERSON>eth<PERSON>[];
