import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getList = '/game/player-complaint/list',
  edit = '/game/player-complaint/save',
  create = '/game/player-complaint/create',
  exportList = '/game/player-complaint/export',
}

/**
 * @description: 获取列表
 */
export function getList(params: any) {
  return defHttp.get({
    url: Api.getList,
    params,
  });
}

/**
 * @description 新增
 */
export function createItem(params: any) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 回复
 */
export function editItem(params: any) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}

/**
 * 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
