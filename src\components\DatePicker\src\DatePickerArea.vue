<template>
  <div class="date-picker">
    <RadioGroup v-model:value="day" button-style="solid" @change="changeTimeTag">
      <template v-for="item in config.radioOptions" :key="`${item.value}`">
        <RadioButton :value="item.value">
          {{ item.label }}
        </RadioButton>
      </template>
    </RadioGroup>
    <RangePicker
      @change="confirmDate"
      v-model:value="state.date"
      v-bind="$attrs"
      :format="config.format"
    />
  </div>
</template>
<script lang="ts">
  import { reactive, defineComponent, ref } from 'vue';
  import { DatePicker, Radio } from 'ant-design-vue';
  import moment from 'moment';
  import { format, radioOptions, timeState } from './config';
  import { string } from 'vue-types';
  export default defineComponent({
    name: 'DatePickerArea',
    components: {
      RangePicker: DatePicker.RangePicker,
      RadioGroup: Radio.Group,
      RadioButton: Radio.Button,
    },
    props: {
      modelValue: {
        type: Array,
      },
      format: {
        type: String,
      },
      day: {
        type: String,
        default: '6',
      },
    },
    setup(props, { emit }) {
      const day = ref('6');
      console.log(props.day);
      day.value = props.day;
      const state = reactive({
        date: timeState[day.value],
      });

      const config = {
        format: props.format || format,
        radioOptions: radioOptions,
      };

      // 使用传入值
      if (props.modelValue && props.modelValue.length >= 2) {
        let now = moment().format(config.format);
        let lastDay = moment().subtract(1, 'days').format(config.format);

        let start = moment(moment(props.modelValue[0]).format(config.format), config.format);
        let end = moment(moment(props.modelValue[1]).format(config.format), config.format);

        let endDay = moment(props.modelValue[1]).format(config.format);

        let dayRange = moment.duration(end.diff(start)).days();

        day.value = '';

        if (dayRange === 0 && [now, lastDay].includes(endDay)) {
          day.value = now === endDay ? '0' : '-1';
        } else if (endDay === now && [6, 30].includes(dayRange)) {
          day.value = dayRange + '';
        }

        state.date = [start, end];
      }

      const _updateValue = () => {
        emit('update:modelValue', [
          moment(state.date[0]).format(config.format),
          moment(state.date[1]).format(config.format),
        ]);
      };

      _updateValue();

      // tab更改
      const changeTimeTag = () => {
        state.date = timeState[day.value];
        _updateValue();
      };

      // 日期选择
      const confirmDate = () => {
        let dayRange = moment.duration(moment(state.date[1]).diff(moment(state.date[0]))).days();
        day.value = dayRange + '';
        _updateValue();
      };

      return {
        day,
        state,
        config,
        changeTimeTag,
        confirmDate,
      };
    },
  });
</script>
<style lang="less" scoped>
  .date-picker {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .ant-calendar-picker {
    flex: 1;
  }
</style>
