// 列表
export interface getListParams {
  assgin_id?: string;
  order_no_title?: string;
  game_id?: string;
  question_id?: string;
  core_account?: string;
  order_type?: string;
  createor?: string;
  start_time?: string;
  end_time?: string;
  qq?: string;
}
// 新增
export interface createParams {
  title: string;
  cp_game_id?: string;
  question_id: string;
  core_account: string;
  qq: string;
  content: string;
  wechat: string;
  game_server_id: string;
  role_id: string;
  role_name: string;
  channel: string;
  desc: string;
  assgin_type: string;
  assgin_id: string;
  order_type: string;
  reply_content: string;
  reply_record: string;
}
// 编辑
export interface editParams extends createParams {
  id: string;
}
// 删除
export interface deleteParams {
  id: string;
}
