import { defHttp } from '/@/utils/http/axios';
import {
  getAddConditionListParams /* , getAddConditionListParamsResultModel */,
  editAddConditionParams,
  createAddConditionParams,
  deleteAddConditionParams,
} from './model/addConditionModel';
import { BasicExportParams } from '../../model/baseModel';
export type { editAddConditionParams, createAddConditionParams };

enum Api {
  getAddCondition = '/config/vip-new-cond-config/list',
  createAddCondition = '/config/vip-new-cond-config/create',
  editAddCondition = '/config/vip-new-cond-config/update',
  deleteAddCondition = '/config/vip-new-cond-config/delete',
  exportAddCondition = '/config/vip-new-cond-config/export',
}

/**
 * @description: 获取管理员列表
 */
export function getAddConditionList(params: getAddConditionListParams) {
  // <getAddConditionListParamsResultModel>
  return defHttp.get({
    url: Api.getAddCondition,
    params,
  });
}

/**
 * @description 修改管理员
 */
export function editAddCondition(params: editAddConditionParams) {
  return defHttp.post({
    url: Api.editAddCondition,
    params,
  });
}

/**
 * @description 新增管理员
 */
export function createAddCondition(params: createAddConditionParams) {
  return defHttp.post({
    url: Api.createAddCondition,
    params,
  });
}
/**
 * @description 删除管理员
 */
export function deleteAddCondition(params: deleteAddConditionParams) {
  return defHttp.post({
    url: Api.deleteAddCondition,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportAddConditionList() {
  return defHttp.get({
    url: Api.exportAddCondition,
  });
}

/**
 * 导出全部:异步
 */
export function exportAddConditionQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportAddCondition,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
