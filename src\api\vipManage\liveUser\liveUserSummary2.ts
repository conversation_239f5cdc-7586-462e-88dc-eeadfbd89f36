import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getUserSummary = '/vip/zb-user-info/list',
  editUserSummary = '/vip/zb-user-info/update',
  allocationUserSummary = '/vip/zb-user-info/batch-assign',
  exportUserSummary = '/vip/zb-user-info/export',
}

// 列表
export function getList(params: any) {
  return defHttp.get({
    url: Api.getUserSummary,
    params,
  });
}

// 批量分配
export function allocationUserSummary(params: any) {
  // <getUserSummaryListParamsResultModel>
  return defHttp.post({
    url: Api.allocationUserSummary,
    params,
  });
}

// 编辑
export function editLiveUser(params: any) {
  return defHttp.post({
    url: Api.editUserSummary,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportLiveUser() {
  return defHttp.get({
    url: Api.exportUserSummary,
  });
}

/**
 * 导出全部:异步
 */
export function exportAllLiveUser(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportUserSummary,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
