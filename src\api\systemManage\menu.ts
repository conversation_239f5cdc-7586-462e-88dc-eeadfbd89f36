import { defHttp } from '/@/utils/http/axios';
import {
  getMenuListParams /* , getMenuListParamsResultModel */,
  createMenuParams,
  editMenuParams,
  deleteMenuParams,
} from './model/menu';
import { BasicExportParams } from '../model/baseModel';
export type { createMenuParams };

enum Api {
  getMenu = '/system/menu/list',
  getMenuTree = '/system/menu/tree',
  createMenu = '/system/menu/create',
  editMenu = '/system/menu/update',
  deleteMenu = '/system/menu/delete',
  exportMenu = '/system/menu/export',
}

/**
 * @description: 获取菜单列表
 */
export function getMenuList(params: getMenuListParams) {
  // <getMenuListParamsResultModel>
  return defHttp.get({
    url: Api.getMenu,
    params,
  });
}
/**
 * @description: 获取菜单列表
 */
export function getMenuTreeList(params: getMenuListParams) {
  // <getMenuListParamsResultModel>
  return defHttp.get({
    url: Api.getMenuTree,
    params,
  });
}
/**
 * @description 新增菜单
 */
export function createMenu(params: createMenuParams) {
  return defHttp.post({
    url: Api.createMenu,
    params,
  });
}

/**
 * @description 修改菜单
 */
export function editMenu(params: editMenuParams) {
  return defHttp.post({
    url: Api.editMenu,
    params,
  });
}
/**
 * @description 删除菜单
 */
export function deleteMenu(params: deleteMenuParams) {
  return defHttp.post({
    url: Api.deleteMenu,
    params,
  });
}

/**
 * 导出全部:异步
 */
export function exportMenuQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportMenu,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
