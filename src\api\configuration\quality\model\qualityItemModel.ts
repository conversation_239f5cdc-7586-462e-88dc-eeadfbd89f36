// 列表
export interface getListParams {
  name?: string;
  category_id?: string;
  type?: string;
}
// 新增
export interface createParams {
  name: string;
  type: number | string;
  score: number | string;
  desc?: string;
  category_id: number | string;
  max_percent: number | string;
}
// 编辑
export interface editParams extends createParams {
  id: string | number;
}
// 删除
export interface deleteParams {
  id: string | number;
}
