{
  // Place your 全局 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  "tpl_vue3_table_simple": {
    "scope": "vue,typescript",
    "prefix": "tpl_vue3_table_simple",
    "body": [
      "<template>",
      "\t<div>",
      "\t\t<!-- 列表 $1 -->",
      "\t\t<BasicTable v-bind=\"stateTable\" @register=\"registerTable\"></BasicTable>",
      "\t</div>",
      "</template>",
      "<script lang=\"ts\">",
      "\timport { getTableSetting, searchFormConfig } from './config';",
      "\timport { BasicTable, TableAction, useTable } from '/@/components/Table';",
      "\timport { defineComponent, reactive } from 'vue';",
      "\timport { getCommonOptions } from '/@/api/sys/common';",
      "\timport { usePermission } from '/@/hooks/web/usePermission';",
      "\t// TODO 根据需求修改api",
      "\timport { getList as getTableList, exportListQueue as exportTableList,} from '/@/api/systemManage/adminGroup';",
      "\texport default defineComponent({",
      "\t\tname: '$2',",
      "\t\tcomponents: { BasicTable, TableAction },",
      "\t\tsetup() {",
      "\t\t\tconst { getAuth } = usePermission();",
      "\t\t\tconst AUTH = getAuth();",

      "\t\t\t//表格配置(动态变化部分)",
      "\t\t\tconst stateTable = reactive({",
      "\t\t\t\tcolumns: [],",
      "\t\t\t});",

      "\t\t\t// 注册表格",
      "\t\t\tconst [registerTable] = useTable({",
      "\t\t\t\tapi: getTableList,",
      "\t\t\t\tdownloadApi: exportTableList,",
      "\t\t\t\tformConfig: searchFormConfig(),",
      "\t\t\t\t...getTableSetting(AUTH),",
      "\t\t\t});",

      "\t\t\t// TODO 表头和选项数据 (根据需求修改请求参数)",
      "\t\t\tgetCommonOptions({",
      "\t\t\t\tschema: 'admin_groups',",
      "\t\t\t\toptions: '',",
      "\t\t\t}).then((data) => {",
      "\t\t\t\tstateTable.columns = data.columns;",
      "\t\t\t});",

      "\t\t\treturn {",
      "\t\t\t\tstateTable,",
      "\t\t\t\tregisterTable,",
      "\t\t\t};",
      "\t\t},",
      "\t});",
      "</script>"
    ],
    "description": "vue3 vben表格(只需要表格的情况下使用)"
  }
}
