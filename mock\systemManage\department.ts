import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../_util';

export default [
  {
    url: '/api/department/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            EXT: '',
            IS_REMOVE: '0',
            NAME: '业务管理部',
          },
        ],
      });
    },
  },
  {
    url: '/api/department/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        NAME: `${id}部`,
      });
    },
  },
  {
    url: '/api/department/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
