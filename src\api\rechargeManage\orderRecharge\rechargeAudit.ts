import { defHttp } from '/@/utils/http/axios';
import { getListParams, editParams, checkParams } from './model/rechargeAuditModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams, checkParams };
enum Api {
  list = '/pay/order-recharge-rebate-review/list',
  recharge = '/pay/order-recharge-rebate-record/select-recharge-order',
  rebate = '/pay/order-recharge-rebate-record/select-rebate-order',
  update = '/pay/order-recharge-rebate-review/update',
  check = '/pay/order-recharge-rebate/get-order-rebate-config',
  exportList = '/pay/payment/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 获取订单返利记录详情 - 支付订单列表
 */
export function getRechargeList(params: getListParams) {
  return defHttp.get({
    url: Api.recharge,
    params,
  });
}
/**
 * @description: 获取订单返利记录详情 - 返利订单列表
 */
export function getRebateList(params: getListParams) {
  return defHttp.get({
    url: Api.rebate,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.update,
    params,
  });
}
/**
 * @description: 获取订单返利记录详情 - 返利订单列表
 */
export function getCheckItem(params: checkParams) {
  return defHttp.get({
    url: Api.check,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
