import { defHttp } from '/@/utils/http/axios';
import { editToolBaseParams } from './model/baseModel';

enum Api {
  getToolbase = '/im/config/detail',
  editToolbase = '/im/config/update',
}

/**
 * @description: 获取工具配置会话分类列表
 */
export function getToolbaseList() {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolbase,
  });
}

/**
 * @description 修改工具配置会话分类
 */
export function editToolbase(params: editToolBaseParams) {
  return defHttp.post({
    url: Api.editToolbase,
    params,
  });
}
