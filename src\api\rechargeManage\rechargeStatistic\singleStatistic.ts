import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/singleStatisticModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams };
enum Api {
  list = '/pay/fill-up-static-game/list',
  payList = '/pay/fill-up-static-payway/list',
  exportList = '/pay/fill-up-static-game/export',
  exporPaytList = '/pay/fill-up-static-payway/export',
}
/**
 * @description: 获取列表
 */
export function getGameList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description: 获取列表
 */
export function getPayList(params: getListParams) {
  return defHttp.get({
    url: Api.payList,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportGameListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
/**
 * @description 导出全部:异步
 */
export function exportPayListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exporPaytList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
