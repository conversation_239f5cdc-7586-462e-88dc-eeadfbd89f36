import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../../_util';

export default [
  {
    url: '/api/tool/seat/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            ext: '否',
            in_group: '0',
            real_name: '叶秋莹',
            receiving: 0,
            state: '下线',
            uid: '37',
            upper: '12',
            user_name: 'yeqiuying',
          },
        ],
      });
    },
  },
  {
    url: '/api/tool/seat/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        upper: `${id}`,
        real_name: '叶秋莹',
        ext: '否',
      });
    },
  },
  {
    url: '/api/tool/seat/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
