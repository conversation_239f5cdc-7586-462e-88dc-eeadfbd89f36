export interface getToolNoticeParams {
  title?: string;
  cp_game_id?: string;
  game_id?: string;
  state?: string;
}

/**
 * @description: 获取权限管理详情params
 */
export interface addToolNoticeParams {
  content: string;
  cp_game_id?: string;
  end_time: string;
  game_id?: string;
  start_time: string;
  state?: string;
  title: string;
}
export interface editToolNoticeParams {
  id: number | string;
  content: string;
  cp_game_id?: string;
  end_time: string;
  game_id?: string;
  start_time: string;
  state?: string;
  title: string;
}

export interface deleteToolNoticeParams {
  id: number | string;
}
