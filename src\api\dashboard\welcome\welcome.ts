import { defHttp } from '/@/utils/http/axios';
import { getVipStatisticParams, getDayStatisticParams } from './model/welcomeModel';

export type { getVipStatisticParams, getDayStatisticParams };

enum Api {
  vipStatistic = '/index/statistics/vip-statistics',
  dayStatistic = '/index/statistics/vip-day-statistics',
  customerDayStatistic = '/index/statistics/customer-day-statistics',
  customerStatistic = '/index/statistics/customer-statistics',
}
/**
 * @description 首页vip数据统计-头部汇总
 */
export function getVipStatistic() {
  return defHttp.get({
    url: Api.vipStatistic,
  });
}
/**
 * @description 首页vip数据统计-折线图
 */
export function getDayStatistic(params: getDayStatisticParams) {
  return defHttp.get({
    url: Api.dayStatistic,
    params,
  });
}
/**
 * @description 首页客服数据统计-折线图
 */
export function getCustomerDayStatistic(params: getDayStatisticParams) {
  return defHttp.get({
    url: Api.customerDayStatistic,
    params,
  });
}
/**
 * @description 首页客服数据统计底部-客服接待排行
 */
export function getCustomerStatistic() {
  return defHttp.get({
    url: Api.customerStatistic,
  });
}
