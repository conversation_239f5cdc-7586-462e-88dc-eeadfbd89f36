<template>
  <div class="sm-echart" ref="chartRef"></div>
</template>
<script>
  import {
    defineComponent,
    reactive,
    nextTick,
    ref,
    toRefs,
    onMounted,
    computed,
    watch,
    onBeforeUnmount,
  } from 'vue';
  import * as echarts from 'echarts';
  import defaultOptions from './defaultOptions';

  const isObject = function (value) {
    let obj =
      value.constructor === Object || Object.prototype.toString.call(value) === '[object Object]';
    return value !== null && value instanceof Object && obj;
  };
  const merge = function (target) {
    const each = function (collection, callback) {
      if (isObject(collection)) {
        let keys = Object.keys(collection);
        return keys.forEach(function (key) {
          return callback(collection[key], key, collection);
        });
      }
      if (collection instanceof Array) {
        return collection.forEach(function (item, i) {
          return callback(item, i, collection);
        });
      }
    };
    let sources = [],
      len = arguments.length - 1;
    while (len-- > 0) {
      sources[len] = arguments[len + 1];
    }
    if (!isObject(target)) return {};
    if (isObject(target)) {
      each(sources, function (source) {
        each(source, function (data, key) {
          if (isObject(data)) {
            if (!target[key] || !isObject(target[key])) {
              target[key] = {};
            }
            merge(target[key], data);
          } else {
            target[key] = data;
          }
        });
      });
      return target;
    }
  };

  export default defineComponent({
    name: 'BasicEchart',
    props: {
      type: {
        type: String,
        default: 'line',
      },
      // 数据
      data: {
        type: Array,
        required: true,
        default: () => [],
      },
      // 渲染方式 (canvas, svg)
      renderer: {
        type: String,
        default: 'canvas',
      },
      // 主题
      theme: {
        type: Object,
        default: () => {},
      },
      // 配置
      options: {
        type: Object,
        default: () => {},
      },
      width: {
        type: String,
        default: 'auto',
      },
      height: {
        type: String,
        default: '200px',
      },
      // event事件
      events: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props, { emit }) {
      let chart = {};
      const chartRef = ref(null);
      const state = reactive({});
      const defaultTheme = {
        name: 'light',
        value: {},
      };
      // 合并数据
      const setEcharts = (data) => {
        if (!props.options) {
          return;
        }
        const opts = merge({}, defaultOptions, props.options);

        if (data.length > 0 && Array.isArray(data)) {
          let series = [];
          for (let i = 0; i < data.length; i++) {
            if (isObject(data[i])) {
              data[i].type = props.type;
              series.push(data[i]);
            } else {
              let objs = {
                data: data[i],
                type: props.type,
              };
              series.push(objs);
            }
          }
          opts.series = series;
        }
        chart.setOption(opts);
        nextTick(() => {
          bindEvents(chart, props.events);
        });
      };
      // 改变chart大小
      const chartResize = () => {
        chart.resize();
      };
      // 绑定事件
      const bindEvents = (instance, events) => {
        const _bindEvent = (eventName, func) => {
          if (typeof eventName === 'string' && typeof func === 'function') {
            instance.on(eventName, (param) => {
              func(param, instance);
            });
          }
        };
        for (const name in events) {
          if (Object.prototype.hasOwnProperty.call(events, name)) {
            _bindEvent(name, events[name]);
          }
        }
      };
      watch(
        () => props.data,
        (datas) => {
          // 清除数据残留
          chart.clear();
          setEcharts(datas);
        }
      );
      watch(
        () => props.options,
        (options) => {
          const opts = merge({}, defaultOptions, options);
          chart.setOption(opts);
        },
        { deep: true }
      );
      onMounted(() => {
        let currentTheme = Object.assign({}, defaultTheme, props.theme);
        echarts.registerTheme(currentTheme.name, currentTheme.value);
        chart = echarts.init(chartRef.value, currentTheme.name, {
          renderer: props.renderer,
          width: props.width,
          height: props.height,
        });
        nextTick(() => {
          setEcharts(props.data);
          setTimeout(() => {
            chartResize();
          }, 100);
        });
        window.addEventListener('resize', chartResize);
      });
      onBeforeUnmount(() => {
        chart.dispose(chartRef.value);
        window.removeEventListener('resize', chartResize);
      });

      return { ...toRefs(state), chartRef, chart };
    },
  });
</script>

<style lang="less">
  .sm-echart {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
