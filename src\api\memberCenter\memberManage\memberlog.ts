import { defHttp } from '/@/utils/http/axios';

enum Api {
  getMemberlogPointList = '/member/account-points-log/list',
  getMemberlogGroupList = '/member/account-energy-log/list',
  getMemberlogLevelList = '/member/member-log/list',
  createMemberlog = '/member/member-log/create',
  getRankInfo = '/member/member-log/rank-info',
}

const state = {
  account_points_log: {
    api: Api.getMemberlogPointList,
  },
  account_energy_log: {
    api: Api.getMemberlogGroupList,
  },
  member_log: {
    api: Api.getMemberlogLevelList,
  },
};

/**
 * 获取数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function getList(params, headers) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });

  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
    headers,
  });
}

export function editMemberLog(params, headers) {
  return defHttp.post({
    url: Api.createMemberlog,
    params,
    headers,
  });
}

export function getRankInfo(params, headers) {
  return defHttp.post({
    url: Api.getRankInfo,
    params,
    headers,
  });
}
