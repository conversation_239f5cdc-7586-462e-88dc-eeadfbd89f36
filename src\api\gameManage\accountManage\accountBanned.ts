import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  editBanParams,
  editUnBanParams,
  activeParams,
} from './model/accountBannedModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editBanParams, editUnBanParams, activeParams };
enum Api {
  list = '/account/user-ban-log/list',
  ban = '/account/user-ban-log/ban',
  unban = '/account/user-ban-log/unban',
  active = '/account/user-ban-log/active',
  exportList = '/account/user-ban-log/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 修改
 */
export function editBanItem(params: { data: editBanParams[] }) {
  return defHttp.post({
    url: Api.ban,
    params,
  });
}
/**
 * @description 修改
 */
export function editUnBanItem(params: { data: editUnBanParams[] }) {
  return defHttp.post({
    url: Api.unban,
    params,
  });
}
/**
 * @description 激活
 */
export function activeItem(params: activeParams) {
  return defHttp.post({
    url: Api.active,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
