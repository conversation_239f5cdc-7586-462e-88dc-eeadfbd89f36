import { defHttp } from '/@/utils/http/axios';
import {
  getMyUserListParams /* , getMyUserListParamsResultModel */,
  editMyUserParams,
  createMyUserParams,
} from './model/myUser';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getMyUser = '/vip/vip-user-info/my-list',
  createMyUser = '/vip/vip-user-info/my-create',
  editMyUser = '/vip/vip-user-info/my-update',
  exportMyUser = '/vip/vip-user-info/my-export',
}

export function getMyUserList(params: getMyUserListParams) {
  // <getMyUserListParamsResultModel>
  return defHttp.get({
    url: Api.getMyUser,
    params,
  });
}

export function editMyUser(params: editMyUserParams) {
  return defHttp.post({
    url: Api.editMyUser,
    params,
  });
}

export function createMyUser(params: createMyUserParams) {
  return defHttp.post({
    url: Api.createMyUser,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportMyUserList() {
  return defHttp.get({
    url: Api.exportMyUser,
  });
}

/**
 * 导出全部:异步
 */
export function exportMyUserQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportMyUser,
    method: 'POST',
    params: {
      ...params,
      self: 1,
    },
    headers: {
      'export-type': 'queue',
    },
  });
}
