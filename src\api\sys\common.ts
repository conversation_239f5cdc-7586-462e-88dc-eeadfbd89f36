import { defHttp } from '/@/utils/http/axios';
import {
  getOptionsParams,
  getExportHistoryParams,
  downloadFileParams,
  headersParams,
} from './model/commonModel';

enum Api {
  GetCommonOptions = '/api/common/options',
  ExportHistory = '/api/common/export-history',
  ExportHistoryQueue = '/api/common/export-queue-num',
  exportUpdateState = '/api/common/export-update-state',
}

/**
 * 获取下拉列表数据
 * @param params
 * @param headers
 * @returns
 */
export const getCommonOptions = (params: getOptionsParams, headers?: headersParams) => {
  return defHttp.post({ url: Api.GetCommonOptions, params, headers });
};

/**
 * 历史导出
 * @param params
 */
export const getExportHistory = (params: getExportHistoryParams, headers?: headersParams) => {
  return defHttp.get({ url: Api.ExportHistory, params, headers });
};
// 导出排队人数
export const getExportHistoryQueueNum = (headers?: headersParams) => {
  return defHttp.get({ url: Api.ExportHistoryQueue, headers });
};
/**
 * 文件下载
 */
export const downloadFile = async (params: downloadFileParams, headers?: headersParams) => {
  return defHttp.post(
    {
      url: Api.ExportHistory,
      params,
      headers,
    },
    { isTransformRequestResult: false }
  );
};
/**
 * 取消文件下载
 */
export const cancelDownloadFile = async (params: downloadFileParams, headers?: headersParams) => {
  return defHttp.post({
    url: Api.exportUpdateState,
    params,
    headers,
  });
};
