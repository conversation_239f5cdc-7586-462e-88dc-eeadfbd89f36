import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list = '/account/transfer-user-analysis/list',
  exportList = '/account/transfer-user-analysis/export',
}

export function getList(params: any) {
  // <getUserLogListParamsResultModel>
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportList() {
  return defHttp.get({
    url: Api.exportList,
  });
}

/**
 * 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
