import { defHttp } from '/@/utils/http/axios';

enum Api {
  getRank = '/member/member-rank-config/list',
  createRank = '/member/member-rank-config/create',
  editRank = '/member/member-rank-config/update',
}

export function getRankList(params, headers) {
  return defHttp.get({
    url: Api.getRank,
    params,
    headers,
  });
}

export function editRank(params, headers) {
  return defHttp.post({
    url: Api.editRank,
    params,
    headers,
  });
}

export function createRank(params, headers) {
  return defHttp.post({
    url: Api.createRank,
    params,
    headers,
  });
}
