import { defHttp } from '/@/utils/http/axios';
import {
  getWorkProblemListParams /* , getWorkProblemListParamsResultModel */,
  editWorkProblemParams,
  createWorkProblemParams,
  deleteWorkProblemParams,
} from './model/workProblemModel';
import { BasicExportParams } from '../../model/baseModel';

export type { editWorkProblemParams, createWorkProblemParams };

enum Api {
  getWorkProblem = '/config/problem-type/list',
  createWorkProblem = '/config/problem-type/create',
  editWorkProblem = '/config/problem-type/update',
  deleteWorkProblem = '/config/problem-type/delete',
  exportWorkProblem = '/config/problem-type/export',
}

/**
 * @description: 获取管理员列表
 */
export function getWorkProblemList(params: getWorkProblemListParams) {
  // <getWorkProblemListParamsResultModel>
  return defHttp.get({
    url: Api.getWorkProblem,
    params,
  });
}

/**
 * @description 修改管理员
 */
export function editWorkProblem(params: editWorkProblemParams) {
  return defHttp.post({
    url: Api.editWorkProblem,
    params,
  });
}

/**
 * @description 新增管理员
 */
export function createWorkProblem(params: createWorkProblemParams) {
  return defHttp.post({
    url: Api.createWorkProblem,
    params,
  });
}
/**
 * @description 删除管理员
 */
export function deleteWorkProblem(params: deleteWorkProblemParams) {
  return defHttp.post({
    url: Api.deleteWorkProblem,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportWorkProblemList() {
  return defHttp.get({
    url: Api.exportWorkProblem,
  });
}

/**
 * 导出全部:异步
 */
export function exportWorkProblemQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportWorkProblem,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
