// #!/usr/bin/env node

import { runBuildConfig } from './buildConf';
import chalk from 'chalk';
import moment from 'moment';

import pkg from '../../package.json';

export const runBuild = async () => {
  try {
    const argvList = process.argv.splice(2);

    // Generate configuration file
    if (!argvList.includes('disabled-config')) {
      // const title = process.title.split(' ');
      const _origin = process.env.npm_lifecycle_script?.replace(/"/g, '').split(' ');
      const buildVersion = _origin ? _origin[_origin.length - 1].trim() : '';
      // const buildVersion = title[title.length - 1].trim();
      await runBuildConfig({
        version: buildVersion,
      });
    }

    console.log(`✨ ${chalk.cyan(`[${pkg.name}]`)}` + ' - build successfully!');
  } catch (error) {
    console.log(chalk.red('vite build error:\n' + error));
    process.exit(1);
  }
};
runBuild();
