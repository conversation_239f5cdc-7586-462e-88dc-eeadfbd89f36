import { useCommonApi } from './common';

enum Api {
  // 总览
  getOverviewList = '/qc/flow-order-stats-overview/list',
  exportOverviewList = '/qc/flow-order-stats-overview/export',

  // 大类
  getCateList = '/qc/flow-order-stats-cate/list',
  exportCateList = '/qc/flow-order-stats-cate/export',

  // 客服
  getItemList = '/qc/flow-order-stats-item/list',
  exportItemList = '/qc/flow-order-stats-item/export',
}

const state = {
  // 总览
  overview: {
    api: Api.getOverviewList,
    exportApi: Api.exportOverviewList,
  },
  // 大类
  cate: {
    api: Api.getCateList,
    exportApi: Api.exportCateList,
  },
  // 客服
  item: {
    api: Api.getItemList,
    exportApi: Api.exportItemList,
  },
};

const { getList, exportListQueue } = useCommonApi(state);

export { getList, exportListQueue };
