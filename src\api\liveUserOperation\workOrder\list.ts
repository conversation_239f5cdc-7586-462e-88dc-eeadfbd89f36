import { defHttp } from '/@/utils/http/axios';
import { getListParams, updateParams } from './model/listModel';
export type { getListParams, updateParams };
enum Api {
  list = '/spc/spc-job-flow/list',
  update = '/spc/spc-job-flow/update',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function updateItem(params: updateParams) {
  return defHttp.post({
    url: Api.update,
    params,
  });
}
