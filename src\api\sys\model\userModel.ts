/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  username: string;
  password: string;
}

/**
 * @description: Get user information
 */
export interface GetUserInfoByUserIdParams {
  userId: string | number;
}

export interface RoleInfo {
  roleName: string;
  value: string;
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoByUserIdModel {
  auth_meta: {};
  department_id: number;
  expire: number;
  group_id: number;
  id: number;
  level: number;
  password: string;
  real_name: string;
  status: number;
  user_name: string;
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  admin: GetUserInfoByUserIdModel;
  token: string;
}

export interface ModifyPwdParams {
  old_pwd: string;
  new_pwd: string;
  confirm_pwd: string;
}
