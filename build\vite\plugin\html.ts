/**
 * Plugin to minimize and use ejs template syntax in index.html.
 * https://github.com/anncwb/vite-plugin-html
 */
import type { Plugin } from 'vite';
import html from 'vite-plugin-html';
import { GLOB_CONFIG_FILE_NAME } from '../../constant';

export function configHtmlPlugin(env: ViteEnv, isBuild: boolean, __APP_INFO__: any) {
  const { VITE_GLOB_APP_TITLE, VITE_PUBLIC_PATH, VITE_GLOB_VITE_BUILD_PKG } = env;
  let path = VITE_PUBLIC_PATH.endsWith('/') ? VITE_PUBLIC_PATH : `${VITE_PUBLIC_PATH}/`;

  if (VITE_GLOB_VITE_BUILD_PKG) {
    // ADD: 每次打包生成个版本号文件夹
    path = VITE_PUBLIC_PATH.endsWith('/')
      ? `${VITE_PUBLIC_PATH}/${__APP_INFO__.version}/`
      : `${VITE_PUBLIC_PATH}/${__APP_INFO__.version}/`;
  }

  // const path = VITE_PUBLIC_PATH.endsWith('/') ? VITE_PUBLIC_PATH : `${VITE_PUBLIC_PATH}/`;

  const getAppConfigSrc = () => {
    return `${path}${GLOB_CONFIG_FILE_NAME}?v=${__APP_INFO__.version}-${new Date().getTime()}`;
  };

  const htmlPlugin: Plugin[] = html({
    minify: isBuild,
    inject: {
      // Inject data into ejs template
      injectData: {
        title: VITE_GLOB_APP_TITLE,
      },
      // Embed the generated app.config.js file
      tags: isBuild
        ? [
            {
              tag: 'script',
              attrs: {
                src: getAppConfigSrc(),
              },
            },
          ]
        : [],
    },
  });
  return htmlPlugin;
}
