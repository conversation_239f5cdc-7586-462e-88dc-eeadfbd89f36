import { defHttp } from '/@/utils/http/axios';
import { getPayRankListParams } from './model/payRankModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getPayRank = '/vip/vip-user-payment-rank/list',
  exportPayRank = '/vip/vip-user-payment-rank/export',
}

export function getPayRankList(params: getPayRankListParams) {
  return defHttp.get({
    url: Api.getPayRank,
    params,
  });
}

/**
 * 导出：异步
 */
export function exportPayRankQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportPayRank,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
