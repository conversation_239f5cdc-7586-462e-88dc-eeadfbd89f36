import { defHttp } from '/@/utils/http/axios';
import { getUserLogListParams /* , getUserLogListParamsResultModel */ } from './model/userAlter';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list = '/vip/vip-core-rank-change-log/list',
  export = '/vip/vip-user-logs/export',
}

export function getList(params: getUserLogListParams) {
  // <getUserLogListParamsResultModel>
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportList() {
  return defHttp.get({
    url: Api.export,
  });
}

/**
 * 导出全部:异步
 */
export function exportQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
