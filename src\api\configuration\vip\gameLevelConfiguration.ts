import { defHttp } from '/@/utils/http/axios';
import {
  getRatingConditionListParams /* , getRatingConditionListParamsResultModel */,
  editRatingConditionParams,
  createRatingConditionParams,
  deleteRatingConditionParams,
} from './model/coreLevelConfigurationModel';
import { BasicExportParams } from '../../model/baseModel';
export type { editRatingConditionParams, createRatingConditionParams };

enum Api {
  list = '/config/game-vip-config/list',
  // create = '/config/game-vip-config/create',
  create = '/config/game-vip-config/create-more',
  edit = '/config/game-vip-config/update',
  delete = '/config/game-vip-config/delete',
  details = '/config/game-vip-config/details',
  exportRatingCondition = '/config/game-vip-config/export',
}

/**
 * @description: 获取列表
 */
export function getList(params: getRatingConditionListParams) {
  // <getRatingConditionListParamsResultModel>
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 修改
 */
export function editItem(params: editRatingConditionParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}

/**
 * @description 新增
 */
export function createItem(params: createRatingConditionParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteItem(params: deleteRatingConditionParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description 详情
 */
export function getItemDetails(params: deleteRatingConditionParams) {
  return defHttp.get({
    url: Api.details,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportRatingConditionList() {
  return defHttp.get({
    url: Api.exportRatingCondition,
  });
}

/**
 * 导出全部:异步
 */
export function exportRatingConditionQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportRatingCondition,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
