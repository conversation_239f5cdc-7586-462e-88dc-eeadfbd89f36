import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/vipUsersChurnModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list1 = '/vip/vip-user-loss-log/list',
  list2 = '/vip/vip-user-loss-date-log/list',
  list3 = '/vip/vip-user-loss-cp-game-log/list',
  list4 = '/vip/vip-user-loss-active-log/list',
  export1 = '/vip/vip-user-loss-log/export',
  export2 = '/vip/vip-user-loss-date-log/export',
  export3 = '/vip/vip-user-loss-cp-game-log/export',
  export4 = '/vip/vip-user-loss-active-log/export',
}

export function getList1(params: getListParams) {
  return defHttp.get({
    url: Api.list1,
    params,
  });
}

export function getList2(params: getListParams) {
  return defHttp.get({
    url: Api.list2,
    params,
  });
}

export function getList3(params: getListParams) {
  return defHttp.get({
    url: Api.list3,
    params,
  });
}

export function getList4(params: getListParams) {
  return defHttp.get({
    url: Api.list4,
    params,
  });
}

export function exportListQueue1(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export1,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}

export function exportListQueue2(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export2,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}

export function exportListQueue3(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export3,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}

export function exportListQueue4(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export4,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
