import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  editParams,
  deleteParams,
  rechargeRebateParams,
} from './model/rechargeRecordModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams, deleteParams, rechargeRebateParams };
enum Api {
  list = '/pay/order-recharge-rebate-record/list',
  recharge = '/pay/order-recharge-rebate-record/select-recharge-order',
  rebate = '/pay/order-recharge-rebate-record/select-rebate-order',
  edit = '/pay/payment/repay',
  delete = '/pay/order-recharge-rebate-record/delete',
  exportList = '/pay/payment/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

export function getRechargeList(params: rechargeRebateParams) {
  return defHttp.get({
    url: Api.recharge,
    params,
  });
}
export function getRebateList(params: rechargeRebateParams) {
  return defHttp.get({
    url: Api.rebate,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 修改
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
