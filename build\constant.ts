/**
 * The name of the configuration file entered in the production environment
 */
export const GLOB_CONFIG_FILE_NAME = '_app.config.js';
export const OUTPUT_DIR = 'dist';
export const getOutput = (env: ViteEnv, __APP_INFO__: any) => {
  let OUTPUT_DIR = 'dist';

  if (env.VITE_GLOB_VITE_BUILD_PKG) {
    OUTPUT_DIR = `dist/${__APP_INFO__.version}`;
  } else {
    OUTPUT_DIR = `dist`;
  }
  return OUTPUT_DIR;
};

export const getBase = (env: ViteEnv, __APP_INFO__: any) => {
  let VITE_PUBLIC_PATH = env.VITE_PUBLIC_PATH;
  if (env.VITE_GLOB_VITE_BUILD_PKG) {
    VITE_PUBLIC_PATH = `${VITE_PUBLIC_PATH}/${__APP_INFO__.version}/`;
  }
  return VITE_PUBLIC_PATH;
};
