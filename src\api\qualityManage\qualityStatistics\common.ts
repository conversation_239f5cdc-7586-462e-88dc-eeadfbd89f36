import { getListParams } from './model/commonModel';
import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

export function useCommonApi(state) {
  /**
   * @description: 获取列表
   */
  function getList(params: getListParams) {
    const keys = Object.keys(params).filter((m) => m !== 'type');
    const curParams = {};
    keys.forEach((m) => {
      curParams[m] = params[m];
    });

    return defHttp.get({
      url: state[params.type].api,
      params: curParams,
    });
  }

  /**
   * @description 导出全部:异步
   */
  function exportListQueue(params: BasicExportParams) {
    const keys = Object.keys(params.query).filter((m) => m !== 'type');
    const queryParams = {};
    keys.forEach((m) => {
      queryParams[m] = params.query[m];
    });
    return defHttp.request({
      url: state[params.query.type].exportApi,
      method: 'POST',
      params: {
        query: queryParams,
        suffix: params.suffix,
      },
      headers: {
        'export-type': 'queue',
      },
    });
  }
  return {
    getList,
    exportListQueue,
  };
}
