import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  createParams,
  editParams,
  deleteParams,
} from './model/smartLexiconCfgModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, editParams, deleteParams };
enum Api {
  list = '/config/intelligent-word/list',
  create = '/config/intelligent-word/create-data',
  edit = '/config/intelligent-word/save-data',
  delete = '/config/intelligent-word/delete-data',
  exportList = '/config/intelligent-word/export',
  detail = '/config/intelligent-word/detail-data',
  get_category_list = '/config/word-category/category-list',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description: 获取列表
 */
export function getCategoryList(params: getListParams) {
  return defHttp.get({
    url: Api.get_category_list,
    params,
  });
}

/**
 * @description: 获取列表
 */
export function detail(params: getListParams) {
  return defHttp.get({
    url: Api.detail,
    params,
  });
}

/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}

/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
