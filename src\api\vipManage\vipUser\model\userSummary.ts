export interface getUserSummaryListParams {
  core_account?: string;
  cp_game_id?: string;
  role_id?: string;
  role_name?: string;
  game_server_id?: string;
  vip_rank?: string;
  last_login_start_time?: string;
  last_login_end_time?: string;
  contact_status?: string;
  kefu_id?: string;
  contact_warning?: string;
  payment_warning?: string;
  login_waring?: string;
  to_core_vip_start_time?: string;
  to_core_vip_end_time?: string;
  last_pay_start_time?: string;
  last_pay_end_time?: string;
  create_start_time?: string;
  create_end_time?: string;
}

export interface editUserSummaryParams {
  id: number | string;
  name?: string;
  content?: string;
  pid?: string;
}
export interface createUserSummaryParams {
  name?: string;
  pid?: string;
  ord?: string;
  content?: string;
}

export interface importUserSummaryParams {
  word?: string;
}

export interface allocationUserSummaryParams {
  origin_kefu_id?: string;
  new_kefu_id?: string;
}
