import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list = '/account/zd-account-list/list',
  create = '/account/zd-account-list/create-more',
  delete = '/account/zd-account-list/delete',
  exportList = '/account/zd-account-list/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: any) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 新增
 */
export function createData(params: any) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description: 删除
 */
export function deleteItem(params: any) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
