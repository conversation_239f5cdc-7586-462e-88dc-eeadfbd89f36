import { defHttp } from '/@/utils/http/axios';
import {
  getDepartmentListParams /* , getAdminUserListParamsResultModel */,
  addDepartmentParams,
  editDepartmentParams,
  deleteDepartmentParams,
} from './model/departmentModel';
import { BasicExportParams } from '../model/baseModel';
export type { addDepartmentParams };

enum Api {
  getDepartment = '/system/department/list',
  addDepartment = '/system/department/create',
  editDepartment = '/system/department/update',
  deleteDepartment = '/system/department/delete',
  exportDepartment = '/system/department/export',
}

/**
 * @description: 获取管理员列表
 */
export function getDepartmentList(params: getDepartmentListParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getDepartment,
    params,
  });
}

/**
 * @description: 获取管理员详情
 */
export function addDepartment(params: addDepartmentParams) {
  return defHttp.post({
    url: Api.addDepartment,
    params,
  });
}
/**
 * @description 修改管理员
 */
export function editDepartment(params: editDepartmentParams) {
  return defHttp.post({
    url: Api.editDepartment,
    params,
  });
}
/**
 * @description 删除管理员
 */
export function deleteDepartment(params: deleteDepartmentParams) {
  return defHttp.post({
    url: Api.deleteDepartment,
    params,
  });
}

/**
 * @description 导出：异步
 */
export function exportDepartmentQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportDepartment,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
