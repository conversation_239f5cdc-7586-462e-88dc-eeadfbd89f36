import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../../_util';

export default [
  {
    url: '/api/tool/notice/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            add_time: '2021-02-03 18:06:00',
            add_user: 'admin',
            content: '撒大声地',
            cp_game_id: '醉江山',
            end_time: '2021-02-04 23:59:59',
            ext: null,
            game_id: '莽荒',
            start_time: '2021-02-03 00:00:00 至 2021-02-04 23:59:59',
            state: '禁用',
            title: '时代大厦多多多多',
          },
        ],
      });
    },
  },
  {
    url: '/api/tool/notice/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        title: `时代大厦多多多多${id}`,
        content: '撒大声地',
        ord: '0',
        start_time: '2021-02-03 00:00:00 至 2021-02-04 23:59:59',
        state: '禁用',
        cp_game_id: '醉江山',
        game_id: '莽荒',
      });
    },
  },
  {
    url: '/api/tool/notice/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
