{"tpl_vue3_schema_select": {"scope": "vue,javascript,typescript", "prefix": "tpl_vue3_schema_select", "body": ["{", "\tfield: '$1',", "\tcomponent: 'Select',", "\tlabel: '$2',", "\tcolProps: colProps,", "\trules: [{ required: true }],", "\tcomponentProps: {", "\t\tshowSearch: true,", "\t\toptionFilterProp: 'label',", "\t\tplaceholder: '$3',", "\t\toptions: [$4], // 选项", "\t},", "},"], "description": "vue3 vben schema select"}}