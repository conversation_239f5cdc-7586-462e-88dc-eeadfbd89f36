{
  // Place your 全局 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  "tpl_vue3_api": {
    "scope": "javascript,typescript",
    "prefix": "tpl_vue3_api",
    "body": [
      "import { defHttp } from '/@/utils/http/axios';",
      "import {",
      "\tgetListParams,",·
      "\tcreateParams,",
      "\teditParams,",
      "\tdeleteParams,",
      "} from './model/menu';",
      "import { BasicExportParams } from '../model/baseModel';",

      "export type {",
      " \tgetListParams,",
      "  \tcreateParams,",
      "  \teditParams,",
      "  \tdeleteParams,",
      "};",

      "enum Api {",
      "\tlist = '/system/menu/list',",
      "\tcreate = '/system/menu/create',",
      "\tedit = '/system/menu/update',",
      "\tdelete = '/system/menu/delete',",
      "\texportList = '/system/menu/export',",
      "}",

      "/**",
      "* @description: 获取列表",
      "*/",
      "export function getList(params: getListParams) {",
      "\treturn defHttp.get({",
      "\t\turl: Api.list,",
      "\t\tparams,",
      "\t});",
      "}",

      "/**",
      "* @description 新增",
      "*/",
      "export function createItem(params: createParams) {",
      "\treturn defHttp.post({",
      "\t\turl: Api.create,",
      "\t\tparams,",
      "\t});",
      "}",

      "/**",
      "* @description 修改",
      "*/",
      "export function editItem(params: editParams) {",
      "\treturn defHttp.post({",
      "\t\turl: Api.edit,",
      "\t\tparams,",
      "\t});",
      "}",
      "/**",
      "* @description 删除",
      "*/",
      "export function deleteItem(params: deleteParams) {",
      "\treturn defHttp.post({",
      "\t\turl: Api.delete,",
      "\t\tparams,",
      "\t});",
      "}",

      "/**",
      "* @description 导出全部:异步",
      "*/",
      "export function exportListQueue(params: BasicExportParams) {",
      "\treturn defHttp.request({",
      "\t\turl: Api.exportList,",
      "\t\tmethod: 'POST',",
      "\t\tparams,",
      "\t\theaders: {",
      "\t\t\t'export-type': 'queue',",
      "\t\t},",
      "\t});",
      "}"
    ],
    "description": "tpl_vue3_api"
  }
}
