import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams, editParams, importParams } from './model/sensitive';
export type { getListParams, createParams, editParams, importParams };
enum Api {
  list = '/wxwork/wx-sensitive-word/list',
  create = '/wxwork/wx-sensitive-word/create',
  edit = '/wxwork/wx-sensitive-word/update',
  tpl = '/wxwork/wx-sensitive-word/get-tpl',
  import = '/wxwork/wx-sensitive-word/import',
  record = '/wxwork/wx-sensitive-log/list',
  // exportList = '/system/menu/export',
  // getTpl
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 新增
 */
export function importData(params: importParams) {
  return defHttp.post({
    url: Api.import,
    params,
  });
}
/**
 * @description: 获取监控列表
 */
export function getRecord(params: getListParams) {
  return defHttp.get({
    url: Api.record,
    params,
  });
}
