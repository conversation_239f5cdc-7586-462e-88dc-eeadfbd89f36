import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../_util';

export default [
  {
    url: '/api/admin_group/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            GROUP_TYPE: '1',
            NAME: '刘世贤',
          },
        ],
      });
    },
  },
  {
    url: '/api/admin_group/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        NAME: `刘世贤${id}`,
      });
    },
  },
  {
    url: '/api/admin_group/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
