import { defHttp } from '/@/utils/http/axios';

enum Api {
  getMemberInfo = '/member/member/list',
  createMemberInfo = '/member/member/create',
  editMemberInfo = '/member/member/update',
  exportList = '/member/member/export',
}

export function getMemberInfoList(params, headers) {
  return defHttp.get({
    url: Api.getMemberInfo,
    params,
    headers,
  });
}

export function editMemberInfo(params, headers) {
  return defHttp.post({
    url: Api.editMemberInfo,
    params,
    headers,
  });
}

export function createMemberInfo(params, headers) {
  return defHttp.post({
    url: Api.createMemberInfo,
    params,
    headers,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params, headers) {
  headers['export-type'] = 'queue';
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers,
  });
}
