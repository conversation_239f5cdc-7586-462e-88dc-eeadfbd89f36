{"tpl_vue3_table_drawer": {"scope": "vue,typescript", "prefix": "tpl_vue3_table_drawer", "body": ["<template>", "\t<div>", "\t\t<!-- 列表: $1 -->", "\t\t<BasicTable v-bind=\"stateTable\" @register=\"registerTable\">", "\t\t\t<template #tableTitle>", "\t\t\t\t<a-button", "\t\t\t\t\ttype=\"primary\"", "\t\t\t\t\tclass=\"mr-2\"", "\t\t\t\t\t:disabled=\"!AUTH.create\"", "\t\t\t\t\t@click=\"handlerCreate\"", "\t\t\t\t>", "\t\t\t\t\t+ 新增", "\t\t\t\t</a-button>", "\t\t\t</template>", "\t\t\t<template #action=\"{ record, column }\">", "\t\t\t\t<TableAction :actions=\"createActions(record, column)\" />", "\t\t\t</template>", "\t\t</BasicTable>", "\t\t<!-- 新增 -->", "\t\t<EditDrawer @register=\"drawerEdit.register\" title=\"新增\" @onUpdate=\"drawerEdit.closeDrawer\" />", "\t</div>", "</template>", "<script lang=\"ts\">", "\timport { defineComponent, reactive, toRaw, ref } from 'vue';", "\timport { getTableSetting, searchFormConfig, useSearchArticles, getEmptyForm } from './config';", "\timport { BasicTable, TableAction, useTable } from '/@/components/Table';", "\timport EditDrawer from './editDrawer/index.vue';", "\timport { useDrawer } from '/@/components/Drawer';        ", "\timport { getCommonOptions } from '/@/api/sys/common';", "\timport { usePermission } from '/@/hooks/web/usePermission';", "\timport { useMessage } from '/@/hooks/web/useMessage';", "\t// TODO 根据需求修改接口名称", "\timport { getList as getTableList, exportListQueue as exportTableList,} from '/@/api/systemManage/adminGroup';", "\tinterface drawerModuleRetures {", "\t\tregister: Function;", "\t\tcloseDrawer: Function;", "\t\tshowDrawer: Function;", "\t\tsendData: Function;", "\t}", "\texport default defineComponent({", "\t\tname: '$2',", "\t\tcomponents: { BasicTable, TableAction, EditDrawer },", "\t\tsetup() {", "\t\t\tconst { getAuth } = usePermission();", "\t\t\tconst AUTH = getAuth();", "\t\t\tconst { createMessage } = useMessage();", "\t\t\tconst drawerTitle = ref('新增');", "\n", "\t\t\t//表格异步数据配置", "\t\t\tconst stateTable = reactive({", "\t\t\t\tcolumns: [],", "\t\t\t});", "\n", "\t\t\t// 注册表格", "\t\t\tconst [registerTable, { reload }] = useTable({", "\t\t\t\tapi: getTableList,", "\t\t\t\tdownloadApi: exportTableList,", "\t\t\t\tformConfig: searchFormConfig(),", "\t\t\t\t...getTableSetting(AUTH),", "\t\t\t});", "\n", "\t\t\t// 抽屉模块", "\t\t\tlet useDrawerModel = () => {", "\t\t\t\tconst [register, { openDrawer: openDrawer }] = useDrawer();", "\t\t\t\tconst showDrawer = () => {", "\t\t\t\t\topenDrawer();", "\t\t\t\t};", "\t\t\t\tconst closeDrawer = () => {", "\t\t\t\t\treload();", "\t\t\t\t};", "\n", "\t\t\t\tconst sendData = (params) => {", "\t\t\t\t\t\topenDrawer(true, params);", "\t\t\t\t};", "\n", "\t\t\t\treturn {", "\t\t\t\t\tregister,", "\t\t\t\t\t<PERSON><PERSON><PERSON><PERSON>,", "\t\t\t\t\tshowD<PERSON>er,", "\t\t\t\t\tsendData,", "\t\t\t\t};", "\t\t\t};", "\n", "\t\t\t// 抽屉注册", "\t\t\tlet drawerInstance = {", "\t\t\t\tdrawerEdit: useDrawerModel() as drawerModuleRetures, // 编辑", "\t\t\t};", "\n", "\t\t\t// TODO 根据需求修改 表头和选项数据", "\t\t\tgetCommonOptions({", "\t\t\t\tschema: 'admin_groups',", "\t\t\t\toptions: '',", "\t\t\t}).then((data) => {", "\t\t\t\tstateTable.columns = data.columns;", "\t\t\t});", "\n", "\t\t\t// TODO 根据需求修改  表格操作", "\t\t\tconst { createActions } = useSearchArticles({", "\t\t\t\thandlerEdit: (record) => {", "\t\t\t\t\t// 编辑", "\t\t\t\t\tdrawerTitle.value='编辑'", "\t\t\t\t\tdrawerInstance.drawerEdit.sendData({columns: toRaw(record)});", "\t\t\t\t},", "\t\t\t\thandlerDelete: async (record) => {", "\t\t\t\t\t// 删除", "\t\t\t\t\tawait deleteItem({ id: record.id });", "\t\t\t\t\tcreateMessage.success('删除成功');", "\t\t\t\t\treload();", "\t\t\t\t},", "\t\t\t\tAUTH: AUTH,", "\t\t\t});", "\t\t\t// 新增", "\t\t\tconst handlerCreate = () => {", "\t\t\t\tdrawerTitle.value='新增'", "\t\t\t\tdrawerInstance.drawerEdit.sendData({columns: getEmptyForm()})", "\t\t\t}", "\n", "\t\t\treturn {", "\t\t\t\tstateTable,", "\t\t\t\tregisterTable,", "\t\t\t\tcreateActions,", "\t\t\t\thandlerCreate,", "\t\t\t\t...drawerInstance,", "\t\t\t\tAUTH,", "\t\t\t};", "\t\t},", "\t});", "</script>"], "description": "vue3 vben表格带有抽屉"}}