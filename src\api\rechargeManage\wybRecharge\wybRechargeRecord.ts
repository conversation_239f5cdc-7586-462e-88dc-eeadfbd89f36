import { defHttp } from '/@/utils/http/axios';
import { getListParams, editParams, deleteParams } from './model/wybRechargeRecordModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, editParams };
enum Api {
  list = '/pay/coin-apply-record/list',
  paylist = '/pay/vw-payment/select-payment-order',
  wyblist = '/pay/payment-coin-order/select-coin-order',
  edit = '/pay/payment/repay',
  delete = '/pay/coin-apply-record/delete',
  exportList = '/pay/payment/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getPayList(params: editParams) {
  return defHttp.get({
    url: Api.paylist,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getWybList(params: editParams) {
  return defHttp.get({
    url: Api.wyblist,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
