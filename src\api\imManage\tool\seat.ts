import { defHttp } from '/@/utils/http/axios';
import {
  getToolSeatParams,
  addToolSeatParams,
  editToolSeatParams,
  deleteToolSeatParams,
} from './model/seatModel';
import { BasicExportParams } from '../../model/baseModel';
export type { addToolSeatParams, editToolSeatParams };

enum Api {
  getToolSeat = '/im/staff/list',
  addToolSeat = '/im/staff/create',
  editToolSeat = '/im/staff/update',
  deleteToolSeat = '/im/staff/delete',
  exportToolSeat = '/im/staff/export',
}

/**
 * @description: 获取工具配置客服座席列表
 */
export function getToolseatList(params: getToolSeatParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolSeat,
    params,
  });
}

/**
 * @description: 获取工具配置客服座席详情
 */
export function addToolseat(params: addToolSeatParams) {
  return defHttp.post({
    url: Api.addToolSeat,
    params,
  });
}

/**
 * @description 修改工具配置客服座席
 */
export function editToolseat(params: editToolSeatParams) {
  return defHttp.post({
    url: Api.editToolSeat,
    params,
  });
}

/**
 * @description 删除工具配置客服座席
 */
export function deleteToolseat(params: deleteToolSeatParams) {
  return defHttp.post({
    url: Api.deleteToolSeat,
    params,
  });
}

/**
 * @description 导出
 */
export function exportToolSeat() {
  return defHttp.post(
    {
      url: Api.exportToolSeat,
    },
    {
      isTransformRequestResult: false,
    }
  );
}

/**
 * 导出全部:异步
 */
export function exportToolSeatQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportToolSeat,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
