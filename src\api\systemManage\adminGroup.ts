import { defHttp } from '/@/utils/http/axios';
import {
  getAdminGroupListParams /* , getAdminUserListParamsResultModel */,
  addAdminGroupParams,
  editAdminGroupParams,
  getGroupPowerParams,
  addGroupGiveParams,
  editGroupGiveParams,
  deleteGroupGiveParams,
  resetGroupGiveParams,
} from './model/adminGroupModel';
import { BasicExportParams } from '../model/baseModel';
export type { addAdminGroupParams };

enum Api {
  getAdminGroup = '/system/group/list',
  addAdminGroup = '/system/group/create',
  editAdminGroup = '/system/group/update',
  exportAdminGroup = '/system/group/export',
  getGroupPower = '/system/menu/getMetaMap',
  addGroupGive = '/system/menu/createMeta',
  editGroupGive = '/system/menu/updateMeta',
  deleteGroupGive = '/system/menu/deleteMeta',
  resetGroupGive = '/system/menu/resetMeta',
}

/**
 * @description: 获取管理员列表
 */
export function getAdminGroupList(params: getAdminGroupListParams) {
  return defHttp.get({
    url: Api.getAdminGroup,
    params,
  });
}

/**
 * @description: 新增管理员详情
 */
export function addAdminGroup(params: addAdminGroupParams) {
  return defHttp.post({
    url: Api.addAdminGroup,
    params,
  });
}

/**
 * @description 修改管理员
 */
export function editAdminGroup(params: editAdminGroupParams) {
  return defHttp.post({
    url: Api.editAdminGroup,
    params,
  });
}

/**
 * @description 获取拥有权限
 */
export function getGroupPower(params: getGroupPowerParams) {
  return defHttp.get({
    url: Api.getGroupPower,
    params,
  });
}

/**
 * @description 新增管理员
 */
export function addGroupGive(params: addGroupGiveParams) {
  return defHttp.post({
    url: Api.addGroupGive,
    params,
  });
}

/**
 * @description 修改管理员
 */
export function editGroupGive(params: editGroupGiveParams) {
  return defHttp.post({
    url: Api.editGroupGive,
    params,
  });
}

/**
 * @description 删除管理员
 */
export function deleteGroupGive(params: deleteGroupGiveParams) {
  return defHttp.post({
    url: Api.deleteGroupGive,
    params,
  });
}

/**
 * @description 重置管理员
 */
export function resetGroupGive(params: resetGroupGiveParams) {
  return defHttp.post({
    url: Api.resetGroupGive,
    params,
  });
}

/**
 * 导出全部:异步
 */
export function exportAdminGroupQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportAdminGroup,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
