import { defHttp } from '/@/utils/http/axios';
import {
  getMyUserListParams /* , getMyUserListParamsResultModel */,
  editMyUserParams,
  createMyUserParams,
  setAllotCustomerParams,
} from './model/myUser';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  // /vip/warning-handle-records/list
  getMyUser = '/vip/warning-handle-records/list',
  // createMyUser = '/vip/vip-user-info/my-create',
  // editMyUser = '/vip/vip-user-info/my-update',
  export = '/vip/warning-handle-records/export',
  userCount = '/vip/warning-handle-records/statistics',
  allotCustomer = '/vip/warning-handle-records/allot-customer',
  setFields = '/vip/warning-handle-records/set-fields',
  getFields = '/vip/warning-handle-records/get-fields',
}

export function countApi(params: any) {
  return defHttp.get({
    url: Api.userCount,
    params,
  });
}

export function getMyUserList(params: getMyUserListParams) {
  return defHttp.get({
    url: Api.getMyUser,
    params,
  });
}
export function setAllotCustomer(params: setAllotCustomerParams) {
  return defHttp.post({
    url: Api.allotCustomer,
    params,
  });
}
export function setFields(params: any) {
  return defHttp.post({
    url: Api.setFields,
    params,
  });
}
export function getFields(params?: any) {
  return defHttp.get({
    url: Api.getFields,
    params,
  });
}

// export function editMyUser(params: editMyUserParams) {
//   return defHttp.post({
//     url: Api.editMyUser,
//     params,
//   });
// }

// export function createMyUser(params: createMyUserParams) {
//   return defHttp.post({
//     url: Api.createMyUser,
//     params,
//   });
// }

/**
 * @description 导出表格数据
 */
// export function exportMyUserList() {
//   return defHttp.get({
//     url: Api.exportMyUser,
//   });
// }

/**
 * 导出全部:异步
 */
export function exportQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export,
    method: 'POST',
    params: {
      ...params,
      self: 1,
    },
    headers: {
      'export-type': 'queue',
    },
  });
}
