export interface editMaintainConfigutationParams {
  contact_warning: string;
  payment_warning: string;
  login_warning: string;
}

export interface getListParams {
  cp_game_id?: string;
  rule_name?: string;
  createid?: string;
}

// 新增
export interface createParams {
  rule_name: string;
  warning_type: string | number;
  cp_game_id?: string;
  config?: any;
}

// 删除
export interface deleteParams {
  id: string | number;
}

export interface editParams extends createParams {
  id: string | number;
}

export interface updateStateParams {
  id: string | number;
  state: number;
}
