<template>
  <div>
    <a-button
      class="margin-lr"
      type="primary"
      @click="addSet"
      v-if="buttonLimits.indexOf('create') != -1"
      >新增</a-button
    >
    <a-button class="margin-lr" type="primary" v-if="buttonLimits.indexOf('template') != -1">
      <a :href="downloadTemplate">下载模板</a>
    </a-button>
    <ImpExcel class="peer margin-lr" @success="loadDataSuccess">
      <a-button type="primary" v-if="buttonLimits.indexOf('import') != -1">Excel导入</a-button>
    </ImpExcel>

    <BasicTable class="basic-table dy-table" v-bind="stateTable" @register="registerTable">
      <template #action="{ record, column, index }">
        <TableAction :actions="createActions(record, column, index)" />
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts">
  import {
    BasicTable,
    useTable,
    EditRecordRow,
    BasicColumn,
    ActionItem,
    TableAction,
  } from '/@/components/Table';
  import { defineComponent, reactive, ref, watch, nextTick, onMounted } from 'vue';
  import { getTableCfi } from './config';
  import { ImpExcel, ExcelData } from '/@/components/Excel';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { cloneDeep } from 'lodash-es';

  export default defineComponent({
    name: 'DyTable',
    components: { BasicTable, TableAction, ImpExcel },
    props: {
      //table数据
      tableSource: {
        type: Array as PropType<any[]>,
        default: () => [],
      },
      //table表头
      tableColumn: {
        type: Array as PropType<any[]>,
        default: () => [],
      },
      // 可操作的按钮
      buttonLimits: {
        type: Array as PropType<any[]>,
        default: () => ['create', 'update', 'delete'],
        // ['create', 'update', 'delete','template'//模板下载,'import'//导入,'export'//导出,]
      },
      // 下载模板地址
      downloadTemplate: {
        type: String,
        default: () => '',
      },
      // 导入表头文字替换成参数
      newExcelData: {
        type: [Object] as PropType<any>,
        default: () => ({}),
      },
      // 去重校验
      repetitionCheck: {
        type: Array as PropType<string[]>,
        default: [],
      },
      // 展示模式，default: 默认模式，simple：简单模式
      mode: {
        type: String,
        default: 'default',
      },
    },
    emits: ['update:tableSource', 'change'],
    setup(props, { emit, attrs }) {
      const { createMessage } = useMessage();
      const onEditChange = ({ column, value, record }) => {
        record[column.dataIndex] = value;
        nextTick(() => {
          stateTable.dataSource.forEach((item) => {
            if (item.index == record.index) {
              item[column.dataIndex] = value;
            }
          });

          emit('update:tableSource', stateTable.dataSource);
          emit('change', stateTable.dataSource);
        });
      };

      //表格配置
      const stateTable = reactive({
        dataSource: props.tableSource ? props.tableSource : [],
        ...attrs,
        onEditChange: props.mode == 'simple' ? onEditChange : null,
      });

      // 注册表格
      const [registerTable, { setTableData, getDataSource, reload }] = useTable({
        ...getTableCfi(),
        columns: props.tableColumn,
        showTableSetting: props.buttonLimits.indexOf('export') != -1,
      });

      const state = reactive({
        isAddSet: true,
      });

      watch(
        () => props.tableSource,
        () => {
          stateTable.dataSource = props.tableSource.map((m, index) => ({ ...m, index }));
        }
      );

      function handleEdit(record: EditRecordRow) {
        record.onEdit(true);
      }

      function handleDelete(record: EditRecordRow, index) {
        stateTable.dataSource = getDataSource();
        stateTable.dataSource.splice(index, 1);
        let changeSource = getDataSource();
        emit('update:tableSource', changeSource);
        emit('change', changeSource);
      }

      const handleSave = async (record, column, index) => {
        await record.onEdit?.(false, true);
        await record.onSubmitEdit();
        let changeSource = getDataSource();
        emit('update:tableSource', changeSource);
        emit('change', changeSource);
      };

      const handleCancel = async (record, index) => {
        await record.onEdit?.(false, false);
        record.onCancelEdit();
      };

      function createActions(record: EditRecordRow, column: BasicColumn, index): ActionItem[] {
        if (props.mode == 'simple') {
          return [
            {
              label: '删除',
              popConfirm: {
                title: '是否删除',
                confirm: handleDelete.bind(null, record, index),
              },
            },
          ];
        }

        if (!record.editable) {
          return [
            {
              label: '编辑',
              onClick: handleEdit.bind(null, record),
            },
            {
              label: '删除',
              popConfirm: {
                title: '是否删除',
                confirm: handleDelete.bind(null, record, index),
              },
            },
          ];
        }
        return [
          {
            label: '保存',
            onClick: handleSave.bind(null, record, column, index),
          },
          {
            label: '取消',
            popConfirm: {
              title: '是否取消',
              confirm: handleCancel.bind(null, record, index),
            },
          },
        ];
      }

      const addSet = () => {
        stateTable.dataSource = getDataSource();
        stateTable.dataSource.push({ editable: true });
        nextTick(() => {
          stateTable.dataSource = getDataSource().map((m, index) => ({ ...m, index }));
          handleEdit(stateTable.dataSource[stateTable.dataSource.length - 1]);
        });
      };

      function loadDataSuccess(excelDataList: ExcelData[]) {
        let header = excelDataList[0].header;
        let dataSource = [] as any;
        for (let i = 0; i < excelDataList.length; i++) {
          dataSource = dataSource.concat(excelDataList[i].results);
        }
        let data = excelDataList[0].results.find((f) => {
          let keys = Object.keys(f);
          return keys.length < header.length;
        });
        for (let i = 0; i < dataSource.length; i++) {
          let obj = dataSource[i];
          for (let key in obj) {
            let newKey = props.newExcelData[key];
            if (newKey) {
              obj[newKey] = obj[key];
              delete obj[key];
            }
          }
        }
        if (!data) {
          if (props.repetitionCheck.length > 0) {
            let filterSource = getDataSource();
            for (let i = 0; i < props.repetitionCheck.length; i++) {
              for (let j = 0; j < dataSource.length; j++) {
                for (let a = j + 1; a < dataSource.length; a++) {
                  if (
                    dataSource[j][props.repetitionCheck[i]] + '' ===
                    dataSource[a][props.repetitionCheck[i]] + ''
                  ) {
                    console.log(a, j, dataSource, dataSource[a], dataSource[j]);
                    createMessage.error(`Excel第${j + 2}条和Excel第${a + 2}行数据重复`);
                    return false;
                  }
                }
                for (let k = 0; k < filterSource.length; k++) {
                  if (
                    filterSource[k][props.repetitionCheck[i]] + '' ===
                    dataSource[j][props.repetitionCheck[i]] + ''
                  ) {
                    createMessage.error(`表格第${k + 1}条和导入数据第${j + 2}行数据重复`);
                    return false;
                  }
                }
              }
            }
          }
          stateTable.dataSource = getDataSource();
          stateTable.dataSource = stateTable.dataSource.concat(dataSource);
          emit('update:tableSource', stateTable.dataSource);
          emit('change', stateTable.dataSource);
          createMessage.success('导入成功');
        } else {
          createMessage.error('导入失败,请检查数据是否缺失。');
        }
      }

      return {
        stateTable,
        registerTable,
        createActions,
        addSet,
        loadDataSuccess,
        onEditChange,
      };
    },
  });
</script>
<style lang="less" scoped>
  /deep/.dy-table {
    .vben-editable-cell__action {
      display: none;
    }

    .ant-input-number {
      width: 100% !important;
    }
  }

  .peer {
    display: inline-block;
  }

  .margin-top {
    margin-top: -4px;
  }

  .margin-lr {
    margin: 0 5px;
  }
</style>
