import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/accountLogModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams };
enum Api {
  list = '/account/account-act-log/list',
  exportList = '/account/account-act-log/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
