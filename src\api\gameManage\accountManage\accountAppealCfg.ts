import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';
enum Api {
  detail = '/game/im-config/detail',
  update = '/game/im-config/save-config',
  exportList = '/game/im-config/export',
}
/**
 * @description: 获取列表
 */
export function getDetail() {
  return defHttp.get({
    url: Api.detail,
  });
}

/**
 * @description: 获取列表
 */
export function updated(params) {
  return defHttp.post({
    url: Api.update,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
