import { defHttp } from '/@/utils/http/axios';
import { getPreformanceDataListParams } from './model/preformanceDataModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getPreformanceData = '/vip/vip-kefu-report/list',
  exportPreformanceData = '/vip/vip-kefu-report/export',
}

export function getPreformanceDataList(params: getPreformanceDataListParams) {
  return defHttp.get({
    url: Api.getPreformanceData,
    params,
  });
}

/**
 * 导出：异步
 */
export function exportPreformanceDataQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportPreformanceData,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
