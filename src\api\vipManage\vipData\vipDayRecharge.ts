import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/vipDayRechargeModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list = '/vip/vip-day-summary/list',
  export = '/vip/vip-day-summary/export',
}

export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * 导出：异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
