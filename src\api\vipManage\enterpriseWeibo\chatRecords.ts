import { defHttp } from '/@/utils/http/axios';
import { http, url } from '/@/utils/https';

enum Api {
  getUsers = '/wxwork/qwb-chat/user-list',
  getRecords = '/wxwork/qwb-chat/log',
}

// 列表
export function getUsers(params: any) {
  return defHttp.get(
    {
      url: Api.getUsers,
      params,
    },
    {
      isTransformRequestResult: false,
    }
  );
}

// 列表
// export function getRecords(params: any) {
//   return defHttp.get(
//     {
//       url: Api.getRecords,
//       params,
//     },
//     {
//       isTransformRequestResult: false,
//       ignoreCancelToken: true,
//     }
//   );
// }

// 列表 { _interrupt: true, _block: true }
export function getRecords(params: any) {
  return http.get(Api.getRecords, params, { _interrupt: true });
}
