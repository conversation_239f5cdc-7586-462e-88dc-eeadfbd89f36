import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getUserSummary = '/vip/live-user-info/list',
  createUserSummary = '/vip/live-user-info/create',
  editUserSummary = '/vip/live-user-info/update',
  importUserSummary = '/vip/live-user-info/import',
  allocationUserSummary = '/vip/live-user-info/batch-assign',
  exportUserSummary = '/vip/live-user-info/export',
  deleteUserSummary = '/vip/live-user-info/delete',
}

// 列表
export function getList(params: any) {
  return defHttp.get({
    url: Api.getUserSummary,
    params,
  });
}

// 新建
export function createLiveUser(params: any) {
  return defHttp.post({
    url: Api.createUserSummary,
    params,
  });
}

// 批量分配
export function allocationUserSummary(params: any) {
  // <getUserSummaryListParamsResultModel>
  return defHttp.post({
    url: Api.allocationUserSummary,
    params,
  });
}

// 导入
export function importLiveUser(params: any) {
  return defHttp.post({
    url: Api.importUserSummary,
    params,
  });
}

// 编辑
export function editLiveUser(params: any) {
  return defHttp.post({
    url: Api.editUserSummary,
    params,
  });
}

// 编辑
export function deleteLiveUser(params: any) {
  return defHttp.post({
    url: Api.deleteUserSummary,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportLiveUser() {
  return defHttp.get({
    url: Api.exportUserSummary,
  });
}

/**
 * 导出全部:异步
 */
export function exportAllLiveUser(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportUserSummary,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
