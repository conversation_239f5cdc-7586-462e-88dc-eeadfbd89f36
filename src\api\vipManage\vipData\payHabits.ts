import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/dataBoardModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getPayHabitsAllList = '/vip/vip-user-pay-daliy-report/list',
  getPayHabitsTimeList = '/vip/vip-user-pay-hour-report/list',
  getPayHabitsLevelList = '/vip/vip-user-pay-distribu/list',

  exportPayHabitsAllList = '/vip/vip-user-pay-daliy-report/export',
  exportPayHabitsTimeList = '/vip/vip-user-pay-hour-report/export',
  exportPayHabitsLevelList = '/vip/vip-user-pay-distribu/export',
}

const state = {
  vip_user_pay_daliy_report: {
    api: Api.getPayHabitsAllList,
    exportApi: Api.exportPayHabitsAllList,
  },
  vip_user_pay_hour_report: {
    api: Api.getPayHabitsTimeList,
    exportApi: Api.exportPayHabitsTimeList,
  },
  vip_user_pay_distribu: {
    api: Api.getPayHabitsLevelList,
    exportApi: Api.exportPayHabitsLevelList,
  },
};

/**
 * 获取数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function getList(params: getListParams) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });

  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
}

/**
 * 导出数据统计列表数据——统一，如后期有特殊，再单独加函数
 * @param params 请求参数
 * @param type 类型
 */
export function exportListQueue(params) {
  /* const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  }); */
  /*
  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
 */
  return defHttp.request({
    url: state[params.type].exportApi,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
