import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  createParams,
  editParams,
  deleteParams,
} from './model/lexiconClassifyModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, editParams, deleteParams };
enum Api {
  list = '/config/word-category/list',
  get_category_list = '/game/active-category/category-list',
  create = '/config/word-category/create',
  edit = '/config/word-category/update',
  delete = '/config/word-category/delete',
  exportList = '/config/word-category/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description: 获取列表
 */
export function getCategoryList(params: getListParams) {
  return defHttp.get({
    url: Api.get_category_list,
    params,
  });
}

/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}

/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
