import { defHttp } from '/@/utils/http/axios';
import {
  getAdminUserListParams /* , getAdminUserListParamsResultModel */,
  editAdminUserParams,
  createAdminUserParams,
  deleteAdminUserParams,
} from './model/adminModel';
import { BasicExportParams } from '../model/baseModel';
export type { createAdminUserParams, editAdminUserParams };

enum Api {
  getAdminUser = '/system/admin-user/list',
  createAdminUser = '/system/admin-user/create',
  editAdminUser = '/system/admin-user/update',
  deleteAdminUser = '/system/admin-user/delete',
  exportAdminUser = '/system/admin-user/export',
}

/**
 * @description: 获取管理员列表
 */
export function getAdminUserList(params: getAdminUserListParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getAdminUser,
    params,
  });
}

/**
 * @description 修改管理员
 */
export function editAdminUser(params: editAdminUserParams) {
  return defHttp.post({
    url: Api.editAdminUser,
    params,
  });
}

/**
 * @description 新增管理员
 */
export function createAdminUser(params: createAdminUserParams) {
  return defHttp.post({
    url: Api.createAdminUser,
    params,
  });
}
/**
 * @description 删除管理员
 */
export function deleteAdminUser(params: deleteAdminUserParams) {
  return defHttp.post({
    url: Api.deleteAdminUser,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportAdminUserList() {
  return defHttp.get({
    url: Api.exportAdminUser,
  });
}

/**
 * 导出全部:异步
 */
export function exportAdminUserQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportAdminUser,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
