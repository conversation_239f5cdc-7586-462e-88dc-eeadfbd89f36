import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams, repeatParams, detailListParams } from './model/massHair';
export type { getListParams, createParams, repeatParams, detailListParams };
enum Api {
  list = '/wxwork/wx-msg-template/list',
  create = '/wxwork/wx-msg-template/create',
  repeat = '/wxwork/wx-msg-template/replay',
  detailList = '/wxwork/wx-msg-send-log/list',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 修改
 */
export function repeatItem(params: repeatParams) {
  return defHttp.post({
    url: Api.repeat,
    params,
  });
}
/**
 * @description 新增
 */
export function detailList(params: detailListParams) {
  return defHttp.get({
    url: Api.detailList,
    params,
  });
}
