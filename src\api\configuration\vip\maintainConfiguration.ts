import { defHttp } from '/@/utils/http/axios';
import {
  editMaintainConfigutationParams,
  getListParams,
  createParams,
  editParams,
  deleteParams,
  updateStateParams,
} from './model/maintainConfigurationModel';

enum Api {
  getMaintainConfigutation = '/config/maintain-config/detail',
  editMaintainConfigutation = '/config/maintain-config/update',
  list = '/config/vip-waring-rule-config/list',
  create = '/config/vip-waring-rule-config/create',
  edit = '/config/vip-waring-rule-config/update',
  updateState = '/config/vip-waring-rule-config/update-state',
  delete = '/config/vip-waring-rule-config/delete',
}

export type { createParams, editMaintainConfigutationParams };

/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}

export function updateStateItem(params: updateStateParams) {
  return defHttp.post({
    url: Api.updateState,
    params,
  });
}

/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description: 获取工具配置会话分类列表
 */
export function getMaintainConfigutationList() {
  return defHttp.get({
    url: Api.getMaintainConfigutation,
  });
}

/**
 * @description 修改工具配置会话分类
 */
export function editMaintainConfigutation(params: editMaintainConfigutationParams) {
  return defHttp.post({
    url: Api.editMaintainConfigutation,
    params,
  });
}
