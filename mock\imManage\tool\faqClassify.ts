import { MockMethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../../_util';

export default [
  {
    url: '/api/tool/faqClassify/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            NAME: '账号问题',
            ORD: '1',
          },
        ],
      });
    },
  },
  {
    url: '/api/tool/faqClassify/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        NAME: `${id}账号问题`,
        ORD: '1',
      });
    },
  },
  {
    url: '/api/tool/faqClassify/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
