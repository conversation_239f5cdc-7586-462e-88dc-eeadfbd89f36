import { defHttp } from '/@/utils/http/axios';
import { getListParams } from './model/errorListModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams };
enum Api {
  // 联系记录
  getErrorList = '/qc/contact-record-error/list',
  exportErrorList = '/qc/contact-record-error/export',

  // 流转工单
  getFlowList = '/qc/flow-order-error/list',
  exportFlowList = '/qc/flow-order-error/export',

  // 在线客服
  getKefuList = '/qc/online-kefu-error/list',
  exportKefuList = '/qc/online-kefu-error/export',
}

const state = {
  contact_record_error: {
    api: Api.getErrorList,
    exportApi: Api.exportErrorList,
  },
  flow_order_error: {
    api: Api.getFlowList,
    exportApi: Api.exportFlowList,
  },
  online_kefu_error: {
    api: Api.getKefuList,
    exportApi: Api.exportKefuList,
  },
};

/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });
  return defHttp.get({
    url: state[params.type].api,
    params: curParams,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  const keys = Object.keys(params).filter((m) => m !== 'type');
  const curParams = {};
  keys.forEach((m) => {
    curParams[m] = params[m];
  });

  return defHttp.request({
    url: state[params.query.type].exportApi,
    method: 'POST',
    params: curParams,
    headers: {
      'export-type': 'queue',
    },
  });
}
