import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list = '/game/xue-user/list',
  recover = '/game/xue-user/recover',
  exportList = '/game/xue-user/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: any) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 回收
 */
export function recoverItem(params: any) {
  return defHttp.post({
    url: Api.recover,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
