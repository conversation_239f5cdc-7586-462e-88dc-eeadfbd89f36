import { defHttp } from '/@/utils/http/axios';
import { getAdminLogListParams, exportAdminLogParams } from './model/adminLogModel';

enum Api {
  getAdminLog = '/system/admin-logs/list',
  exportAdminLog = '/system/admin-logs/export',
}

/**
 * @description: 获取管理员列表
 */
export function getAdminLogList(params: getAdminLogListParams) {
  return defHttp.get({
    url: Api.getAdminLog,
    params,
  });
}

/**
 * 导出：异步
 */
export function exportAdminLogQueue(params: exportAdminLogParams) {
  return defHttp.request({
    url: Api.exportAdminLog,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
