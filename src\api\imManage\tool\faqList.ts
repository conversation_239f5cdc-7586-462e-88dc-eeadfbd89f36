import { defHttp } from '/@/utils/http/axios';
import {
  getToolFaqListParams,
  createToolFaqListParams,
  editToolFaqListParams,
  deleteToolFaqListParams,
} from './model/faqListModel';

import { BasicExportParams } from '../../model/baseModel';

enum Api {
  getToolFaqList = '/im/ques-list/list',
  createToolFaqList = '/im/ques-list/create',
  editToolFaqList = '/im/ques-list/update',
  deleteToolFaqList = '/im/ques-list/delete',
  exportToolFaqList = '/im/ques-list/export',
}

/**
 * @description: 获取问题分类列表
 */
export function getToolFaqList(params: getToolFaqListParams) {
  // <getAdminUserListParamsResultModel>
  return defHttp.get({
    url: Api.getToolFaqList,
    params,
  });
}

/**
 * @description 新增问题分类
 */
export function createToolFaqList(params: createToolFaqListParams) {
  return defHttp.post({
    url: Api.createToolFaqList,
    params,
  });
}
/**
 * @description 修改问题分类
 */
export function editToolFaqList(params: editToolFaqListParams) {
  return defHttp.post({
    url: Api.editToolFaqList,
    params,
  });
}

/**
 * @description 删除问题分类
 */
export function deleteToolFaqList(params: deleteToolFaqListParams) {
  return defHttp.post({
    url: Api.deleteToolFaqList,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportToolFaqList() {
  return defHttp.post({
    url: Api.exportToolFaqList,
  });
}

/**
 * 导出全部:异步
 */
export function exportToolFaqListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportToolFaqList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
