import { defHttp } from '/@/utils/http/axios';
import { getAllConvListParams, getCloseParams } from './model/allConversationModel';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  // 会话分析
  getImConvList = '/im/conv/list',
  exportImConvList = '/im/conv/export',
  convVait = '/im/conv/wait',
  convClose = '/im/conv/close',
}

/**
 * 获取我解决的会话列表
 * @param params 请求参数
 * @param type 类型
 */
export function getImConvList(params: getAllConvListParams) {
  return defHttp.get({
    url: Api.getImConvList,
    params,
  });
}

/**
 * 导出
 */
export function exportImConvList() {
  return defHttp.post({
    url: Api.exportImConvList,
  });
}

/**
 * 导出全部:异步
 */
export function exportImConvListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportImConvList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}

/**
 * 获取排队人数
 */
export function getConvWait() {
  return defHttp.get({
    url: Api.convVait,
  });
}

/**
 * 关闭状态
 */
export function getConvClose(params: getCloseParams) {
  return defHttp.get({
    url: Api.convClose,
    params,
  });
}
