import { defHttp } from '/@/utils/http/axios';
import { BasicExportParams } from '../../model/baseModel';

enum Api {
  list = '/vip/zd-user-info/list',
  exportList = '/vip/zd-user-info/export',
}

export function getZdUserList(params: any) {
  // <getUserLogListParamsResultModel>
  return defHttp.get({
    url: Api.list,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportZdUserList() {
  return defHttp.get({
    url: Api.exportList,
  });
}

/**
 * 导出全部:异步
 */
export function exportZdUserListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
