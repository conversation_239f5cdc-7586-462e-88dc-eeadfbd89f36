import { defHttp } from '/@/utils/http/axios';
import {
  getListParams /* , getListParamsResultModel */,
  editParams,
  createParams,
  deleteParams,
} from './model/gameActivityModel';
import { BasicExportParams } from '../../model/baseModel';

export type { editParams, createParams };

enum Api {
  list = '/config/game-active/list',
  create = '/config/game-active/create',
  edit = '/config/game-active/update',
  delete = '/config/game-active/delete',
  detail = '/config/game-active/detail',
  get_category_list = '/game/active-category/category-list',
  export = '/config/game-active/export',
}

/**
 * @description: 获取管理员列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getCategoryList(params: getListParams) {
  return defHttp.get({
    url: Api.get_category_list,
    params,
  });
}
/**
 * @description 修改
 */
export function edit(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 详情
 */
export function detail(params: editParams) {
  return defHttp.get({
    url: Api.detail,
    params,
  });
}
/**
 * @description 新增管理员
 */
export function create(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 删除管理员
 */
export function del(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}

/**
 * @description 导出表格数据
 */
export function exportList() {
  return defHttp.get({
    url: Api.export,
  });
}

/**
 * 导出全部:异步
 */
export function exportQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.export,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
