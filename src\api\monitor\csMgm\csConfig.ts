import { defHttp } from '/@/utils/http/axios';
import { getListParams, createParams, editParams } from './model/csConfig';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, editParams };
enum Api {
  list = '/wxwork/wx-customer-service/list',
  create = '/wxwork/wx-customer-service/create',
  edit = '/wxwork/wx-customer-service/update',
  exportList = '/system/menu/export',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}
/**
 * @description 修改
 */
export function editItem(params: editParams) {
  return defHttp.post({
    url: Api.edit,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
