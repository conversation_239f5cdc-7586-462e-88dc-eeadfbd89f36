import { Mock<PERSON>ethod } from 'vite-plugin-mock';
import { /* resultError, */ resultSuccess } from '../_util';

export default [
  {
    url: '/api/admin_user/data',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { page, pageSize } = query;

      return resultSuccess({
        total: 400,
        pageSize: pageSize,
        page: page,
        [`data|${pageSize}`]: [
          {
            'ID|+1': 1,
            USER_NAME: 'admin',
            REAL_NAME: 'admin',
            PASSWORD: 'f843fb9ad16c064d6ca9a7226c94a348',
            GROUP_ID: '超级管理员',
            LEVEL: '超级管理员',
            STATUS: '禁止登录',
            EXT_GROUP_ID: '0',
            SEX: '1',
            EMAIL: '<EMAIL>',
            DEPARTMENT_ID: '技术部',
            ARRANGE_ALLOW: '不允许',
          },
        ],
      });
    },
  },
  {
    url: '/api/admin_user/detail',
    timeout: 1000,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      return resultSuccess({
        USER_NAME: `admin1__${id}`,
        REAL_NAME: 'admin',
        PASSWORD: '',
        EMAIL: '<EMAIL>',
        SEX: '1',
        LEVEL: '1',
        DEPARTMENT_ID: '1',
        STATUS: '0',
        GROUP_ID: '0',
      });
    },
  },
  {
    url: '/api/admin_user/edit',
    timeout: 1000,
    method: 'post',
    response: ({ body }) => {
      return resultSuccess({
        body,
      });
    },
  },
] as MockMethod[];
