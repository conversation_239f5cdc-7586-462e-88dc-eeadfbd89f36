import { defHttp } from '/@/utils/http/axios';
import {
  getListParams,
  createParams,
  deleteParams,
  transferParams,
} from './model/accountAssociatedModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams, createParams, deleteParams, transferParams };
enum Api {
  list = '/account/contact-user/list',
  create = '/account/contact-user/relate',
  delete = '/account/contact-user/remove',
  exportList = '/account/contact-user/export',
  transfer = '/account/contact-user/transfer',
}

// 注册新账号
export function transfer(params: transferParams) {
  return defHttp.post({
    url: Api.transfer,
    params,
  });
}

/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description 新增
 */
export function createItem(params: createParams) {
  return defHttp.post({
    url: Api.create,
    params,
  });
}

/**
 * @description 删除
 */
export function deleteItem(params: deleteParams) {
  return defHttp.post({
    url: Api.delete,
    params,
  });
}
/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}
