{
  // Place your 全局 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  "tpl_vue3_table_edit_drawer": {
    "scope": "vue,typescript",
    "prefix": "tpl_vue3_table_edit_drawer",
    "body": [
      "<template>",
      "<BasicDrawer",
      "\t\tv-bind=\"\\$attrs\"",
      "\t\t@register=\"register\"",
      "\t\t@ok=\"handleOk\"",
      "\t\tdestroyOnClose",
      "\t\tshowFooter",
      ">",
      "\t\t<BasicForm @register=\"registerForm\"></BasicForm>",
      "</BasicDrawer>",
      "</template>",
      "<script lang=\"ts\">",
      "\timport { defineComponent, toRaw } from 'vue';",
      "\timport { FormSchema } from '/@/components/Table';",
      "\timport { BasicDrawer, useDrawerInner } from '/@/components/Drawer';",
      "\timport { BasicForm, useForm } from '/@/components/Form/index';",
      "\timport { editFormConfig } from './config';",
      "\timport { useMessage } from '/@/hooks/web/useMessage';",
      "\t// TODO：按照需求更换接口",
      "\timport { editItem, createItem } from '/@/api/systemManage/adminUser';",
      "\timport type { createParams } from '/@/api/systemManage/adminUser';",

      "\texport default defineComponent({",
      "\t\tcomponents: { BasicDrawer, BasicForm },",
      "\t\temits: ['onUpdate', 'register'],",
      "\t\tsetup(props, { emit }) {",
      "\t\t\tlet id = '';",
      "\t\t\tconst { createMessage } = useMessage();",

      "\t\t\t// 注册表单",
      "\t\t\tconst [",
      "\t\t\t\tregisterForm,",
      "\t\t\t\t{ setFieldsValue, validateFields, getFieldsValue, clearValidate, setProps },",
      "\t\t\t] = useForm({",
      "\t\t\t\tlabelWidth: 110,",
      "\t\t\t\tshowActionButtonGroup: false,",
      "\t\t\t\tactionColOptions: {",
      "\t\t\t\t\tspan: 24,",
      "\t\t\t\t},",
      "\t\t\t});",

      "\t\t\t// TODO: 初始化表单数据",
      "\t\t\tconst [register, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner((data) => {",
      "\t\t\t\tchangeLoading(true);",
      "\t\t\t\tlet columns = data.columns;",
      "\t\t\t\tlet options = toRaw(data.options);",
      "\t\t\t\tid = columns.id;",

      "\t\t\t\tlet schemas: FormSchema[] = editFormConfig(options);",
      "\t\t\t\tsetProps({schemas})",

      "\t\t\t\tlet value = {};",
      "\t\t\t\tObject.keys(columns).forEach((key) => {",
      "\t\t\t\t\tvalue[key] = columns[key] + '';",
      "\t\t\t\t});",

      "\t\t\t\tsetFieldsValue(value);",
      "\t\t\t\tclearValidate();",
      "\t\t\t\tchangeLoading(false);",
      "\t\t\t});",

      "\t\t\t// TODO: 请求修改/新增接口，关闭抽屉",
      "\t\t\tconst handleOk = async () => {",
      "\t\t\tchangeOkLoading(true)",
      "\t\t\t\tvalidateFields()",
      "\t\t\t\t\t.then(async () => {",
      "\t\t\t\t\t\tlet formData = getFieldsValue() as createParams;",
      "\t\t\t\t\t\ttry {",
      "\t\t\t\t\t\t\tif (!id) {",
      "\t\t\t\t\t\t\t\tawait createItem({",
      "\t\t\t\t\t\t\t\t\t...formData,",
      "\t\t\t\t\t\t\t\t});",
      "\t\t\t\t\t\t\t} else {",
      "\t\t\t\t\t\t\t\tawait editItem({",
      "\t\t\t\t\t\t\t\t\tid: id,",
      "\t\t\t\t\t\t\t\t\t...formData,",
      "\t\t\t\t\t\t\t\t});",
      "\t\t\t\t\t\t\t}",
      "\t\t\t\t\t\t\tcloseDrawer();",
      "\t\t\t\t\t\t\tcreateMessage.success('更新成功');",
      "\t\t\t\t\t\t\temit('onUpdate', { type: 'reload' });",
      "\t\t\t\t\t\t\tchangeOkLoading(false)",
      "\t\t\t\t\t\t} catch (error) {",
      "\t\t\t\t\t\t\tchangeOkLoading(false)",
      "}",
      "\t\t\t\t\t})",
      "\t\t\t\t\t.catch(() => {",
      "\t\t\t\t\t\tcreateMessage.error('请正确填写表单');",
      "\t\t\t\t\t\t\tchangeOkLoading(false)",
      "\t\t\t\t\t});",
      "\t\t\t};",

      "\t\t\treturn { register, registerForm, handleOk };",
      "\t\t},",
      "\t});",
      "</script>"
    ],
    "description": "tpl_vue3_table_edit_drawer"
  }
}
