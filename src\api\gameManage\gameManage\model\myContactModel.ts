/**
 * @description: 获取管理员管理接口params
 */
export interface getMyContactListParams {
  order_no_title?: string;
  cp_game_id?: string;
  game_id?: string;
  question_id?: string;
  core_account?: string;
  role_id?: string;
  qq?: string;
  wechat?: number;
  createor?: number;
  start_time?: number;
  end_time?: number;
}

export interface createMyContactParams {
  title: string;
  game_id: string;
  question_id: string;
  core_account: string;
  qq: string;
  content: string;
  wechat?: string;
  game_server_id?: string;
  role_id?: string;
  role_name?: string;
  channel_id: string;
  score: string;
  desc?: string;
}

export interface updateMyContactParams {
  id: number;
  title: string;
  game_id: string;
  question_id: string;
  core_account: string;
  qq: string;
  content: string;
  wechat?: string;
  game_server_id?: string;
  role_id?: string;
  role_name?: string;
  channel_id: string;
  score: string;
  desc?: string;
}

export interface deleteMyContactParams {
  id: string | number;
}
