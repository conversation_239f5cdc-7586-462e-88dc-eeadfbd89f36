import { defHttp } from '/@/utils/http/axios';
import { getListParams, getDeatilParams } from './model/registrationSearchModel';
import { BasicExportParams } from '../../model/baseModel';
export type { getListParams };
enum Api {
  list = '/account/user-reg/list',
  detail = '/account/user-reg/detail',
  resetName = '/account/user-reg/reset-user-name',
  exportList = '/account/user-reg/export',
  resetCount = '/account/user-reg/reset-user-name-times',
}
/**
 * @description: 获取列表
 */
export function getList(params: getListParams) {
  return defHttp.get({
    url: Api.list,
    params,
  });
}
/**
 * @description: 获取列表
 */
export function getDeatil(params: getDeatilParams) {
  return defHttp.get({
    url: Api.detail,
    params,
  });
}

/**
 * @description: 实名重置
 */
export function resetName(params: getDeatilParams) {
  return defHttp.post({
    url: Api.resetName,
    params,
  });
}

/**
 * @description 导出全部:异步
 */
export function exportListQueue(params: BasicExportParams) {
  return defHttp.request({
    url: Api.exportList,
    method: 'POST',
    params,
    headers: {
      'export-type': 'queue',
    },
  });
}

export function resetCount(params: getDeatilParams) {
  return defHttp.post({
    url: Api.resetCount,
    params,
  });
}
